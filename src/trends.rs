use std::{collections::{HashMap, VecDeque}, sync::{Arc, Mutex}, str::FromStr};
use lazy_static::lazy_static;
use solana_sdk::pubkey::Pubkey;

//const detect_trade_treshold: f64 = 1.0;


#[derive(Debug, <PERSON>lone,PartialEq)]
pub enum TradeState {
    None,
    Monitor,
    Ready,
    Buying,
    Bought,
    BuyFailed,
    BuyTimedOut,
    Selling,
    Sold,
    SellFailed,
    SellTimedOut,
    Error,
    Unknown,
    Burning,
    Burned,
    Withdrawn,
    Cancelled

}



#[derive(Debug, Clone)]
pub struct MintData {
    pub scam: bool,
    pub num_of_trades: u64,
    pub mint: Pubkey, // Mint address as a string
    pub pool: Pubkey, // Pool address for the mint
    pub sol_liquidity: f64, // Amount of SOL liquidity in the pool
    pub trade_state: TradeState,
    pub price: f64,
    pub start_price: f64,
    pub start_price_base: f64,
    pub base_mint: String,
    pub quote_mint: String,
    pub bought: bool, // Flag to indicate if the mint has been bought
    pub sold: bool, // Flag to indicate if the mint has been sold
    pub buy_price: f64,
    pub buy_time: Option<std::time::SystemTime>,
    pub sell_time: Option<std::time::SystemTime>,
    pub sell_price: f64,
    pub sell_retry_count: u32,
    pub buy_token_amount: f64,
    pub sell_token_amount: f64,
    pub sell_sol_amount: f64,
    pub buy_sol_amount: f64,
    pub token_decimals: u8, // Number of decimals for the token
    pub sol_reserves: f64, // SOL reserves in the pool
    pub token_reserves: f64, // Token reserves in the pool
    pub created: std::time::SystemTime,
    pub last_updated: std::time::SystemTime,
    pub creator: Pubkey, // Optional creator address
    pub fee_account: Pubkey, // Optional fee account address
    pub volume: f64,
    pub market_cap: f64,
    pub ath_mcap: f64,
    pub recent_trade_timestamps: Vec<std::time::SystemTime>, 
    pub withdrawn: bool, // Flag to indicate if the mint has been withdrawn
    pub tx_counter: u32,
    pub multi_tx: i32, // Number of multiple transactions
    pub single_tx: i32, // Number of single transactions
    pub unit_limit: String, // Unit limit as a string
    pub unit_price: String, // Unit price as a string
    pub tip_amount: f64, // Tip amount in float
    pub bot_amount: i32, // Bot amount as an integer
    pub bot_sol_amount: f64, 
    pub window : PriceWindow, // Sliding window for price trends
    pub unit_occurrences: HashMap<u32, i32>, // Map to track occurrences of specific unit values
    pub max_pct: f64, // Maximum percentage change allowed
    pub enabled: bool, // Flag to indicate if the mint is enabled




    
}

impl Default for MintData {
    fn default() -> Self {
        Self {
            scam: false,
            num_of_trades: 0,
            mint : Pubkey::default(), // Default to empty string
            pool: Pubkey::default(), // Default to zeroed Pubkey
            sol_liquidity: 0.0, // Default to zero
            base_mint: String::new(),
            quote_mint: String::new(),
            trade_state: TradeState::None,
            price: 0.0,
            start_price: 0.0,
            start_price_base: 0.0,
            bought: false, // Default to false
            sold: false, // Default to false
            buy_price: 0.0,
            buy_time: None,
            sell_time: None,
            sell_price: 0.0,
            sell_retry_count: 0,
            buy_token_amount: 0.0,
            buy_sol_amount: 0.0,
            sell_token_amount: 0.0,
            sell_sol_amount: 0.0,
            token_decimals: 0, // Default to zero decimals
            sol_reserves: 0.0, // Default to zero reserves
            token_reserves: 0.0, // Default to zero reserves
            created: std::time::SystemTime::now(),
            last_updated: std::time::SystemTime::now(),
            creator: Pubkey::from_str("8N3GDaZ2iwN65oxVatKTLPNooAVUJTbfiVJ1ahyqwjSk").unwrap(), // WSOL pubkey
            fee_account: Pubkey::default(), // Default to zeroed Pubkey
            volume: 0.0,
            market_cap: 0.0,
            ath_mcap: 0.0,
            recent_trade_timestamps: Vec::new(),
            withdrawn: false, // Default to false
            tx_counter: 0,
            multi_tx: 0,
            single_tx: 0,
            unit_limit: String::new(),
            unit_price: String::new(),
            tip_amount: 0.0,
            bot_amount: 0,
            bot_sol_amount: 0.0,
            window: PriceWindow::new(), // Initialize the sliding window
            unit_occurrences: HashMap::new(), // Initialize the unit occurrences map
            max_pct: 0.0, // Default to 10.0
            enabled: false, // Default to true
        }
    }
}

lazy_static! {
    #[derive(Debug)]
    pub static ref MINT_DATA_CACHE: Arc<Mutex<HashMap<String, MintData>>> = 
        Arc::new(Mutex::new(HashMap::new()));
}

// Helper functions to interact with the global mint data cache
pub fn update_mint_data(mint: &str, data: MintData) {
    if let Ok(mut cache) = MINT_DATA_CACHE.lock() {
        cache.insert(mint.to_string(), data.clone());
        let pool_key = data.pool.to_string();
        if !cache.contains_key(&pool_key) {
            cache.insert(pool_key, data.clone());
        }
        
        // Debug: print cache size after update
        //println!("Cache size after update: {}", cache.len());
    } else {
        eprintln!("Failed to acquire lock for update_mint_data");
    }
}

pub fn get_mint_data(mint: &str) -> Option<MintData> {
    if let Ok(cache) = MINT_DATA_CACHE.lock() {
        cache.get(mint).cloned()
    } else {
        None
    }
}

pub fn get_mint_by_pool(pool: &str) -> Option<MintData> {
    if let Ok(cache) = MINT_DATA_CACHE.lock() {
        cache.get(pool).cloned()
    } else {
        None
    }
}

pub fn increment_trades(mint: &str) {
    if let Ok(mut cache) = MINT_DATA_CACHE.lock() {
        let entry = cache.entry(mint.to_string()).or_default();
        let now = std::time::SystemTime::now();

        // Add the current timestamp to the sliding window
        entry.recent_trade_timestamps.push(now);

        // Remove timestamps outside the sliding window (e.g., older than 5 seconds)
        entry.recent_trade_timestamps.retain(|&timestamp| {
            if let Ok(duration) = now.duration_since(timestamp) {
                duration.as_secs() <= 5
            } else {
                false
            }
        });

        entry.num_of_trades += 1;
        entry.last_updated = now;
    }
}

pub fn update_price(mint: &str, new_price: f64) {



    if let Ok(mut cache) = MINT_DATA_CACHE.lock() {
        let entry = cache.entry(mint.to_string()).or_default();
        entry.price = new_price;
        entry.last_updated = std::time::SystemTime::now();
    }
}
// Create remove mint from cache
pub fn remove_mint(mint: &str) {
    if let Ok(mut cache) = MINT_DATA_CACHE.lock() {
        cache.remove(mint);
    }
}

pub fn list_all_mints() -> Vec<String> {
    if let Ok(cache) = MINT_DATA_CACHE.lock() {
        cache.keys().cloned().collect()
    } else {
        Vec::new()
    }
}

pub fn get_trade_rate(mint: &str) -> Option<f64> {
    if let Ok(cache) = MINT_DATA_CACHE.lock() {
        if let Some(entry) = cache.get(mint) {
            let now = std::time::SystemTime::now();
            let valid_timestamps: Vec<_> = entry.recent_trade_timestamps.iter().filter(|&&timestamp| {
                if let Ok(duration) = now.duration_since(timestamp) {
                    duration.as_secs() <= 5
                } else {
                    false
                }
            }).collect();

            let elapsed_seconds = 5.0; // Sliding window duration
            return Some(valid_timestamps.len() as f64 / elapsed_seconds);
        }
    }
    None
}

pub fn detect_high_trade_rate(mint: &str, threshold: f64) -> bool {
    if let Some(trade_rate) = get_trade_rate(mint) {
        return trade_rate > threshold;
    }
    false
}

pub fn print_mint_data_summary() {
    if let Ok(cache) = MINT_DATA_CACHE.lock() {
        println!("=== Mint Data Cache Summary ===");
        println!("Total mints tracked: {}", cache.len());
        
        for (mint, data) in cache.iter() {
            let trade_rate = get_trade_rate(mint).unwrap_or(0.0);
            let is_high_rate = detect_high_trade_rate(mint, 10.0); // Example threshold: 10 trades/sec
            println!(
                "Mint: {} | Price: ${:.8} | Trades: {} | Volume: ${:.2} | Market Cap: ${:.2} | Trade Rate: {:.2}/sec | High Rate: {}",
                mint, data.price, data.num_of_trades, data.volume, data.market_cap, trade_rate, is_high_rate
            );
        }
    } else {
        println!("Failed to access mint data cache");
    }
}





pub const WINDOW_SIZE: usize = 10; // e.g. last 60 ticks

#[derive(Debug, Clone)]
pub struct PricePoint {
    pub timestamp: u64, // or chrono::DateTime
    pub price: f64,
    pub buy_amount_usd: f64,
    pub sell_amount_usd: f64,
    pub is_dev:bool,

}


#[derive(Debug, Clone)]
pub struct TradeStyle {
    pub single_tx: i32,
    pub multi_tx: i32,
    pub trades_per_second: i32,

}

#[derive(Debug, Clone)]
pub struct PriceWindow {
    pub buffer: VecDeque<PricePoint>,
}
 
 impl PriceWindow {
    pub fn new() -> Self {
        Self { buffer: VecDeque::with_capacity(WINDOW_SIZE) }
    }

    pub fn push(&mut self, point: PricePoint) {
        if self.buffer.len() == WINDOW_SIZE {
            self.buffer.pop_front();
        }
        self.buffer.push_back(point);
    }

    // pub fn is_full(&self) -> bool {
    //     self.buffer.len() == WINDOW_SIZE
    // }
    pub fn get_trend(&self) -> Option<f64> {
        if self.buffer.len() < 2 {
            return None;
        }
        let first = self.buffer.front().unwrap().price;
        let last = self.buffer.back().unwrap().price;
        Some(last - first)
    }

    pub fn get_trend_pct(&self) -> Option<f64> {
        if self.buffer.len() < 2 {
            return None;
        }
        let first = self.buffer.front().unwrap().price;
        let last = self.buffer.back().unwrap().price;
        if first == 0.0 {
            None
        } else {
            Some((last - first) / first * 100.0)
        }
    }

    // pub fn trend_label(&self, threshold: f64) -> Option<&'static str> {
    //     self.get_trend().map(|trend| {
    //         if trend > threshold {
    //             "up"
    //         } else if trend < -threshold {
    //             "down"
    //         } else {
    //             "flat"
    //         }
    //     })
    // }
    // pub fn regression_slope(&self) -> Option<f64> {
    //     let n = self.buffer.len();
    //     if n < 2 { return None; }
    //     let n = n as f64;
    //     let sum_x: f64 = (0..self.buffer.len()).map(|x| x as f64).sum();
    //     let sum_y: f64 = self.buffer.iter().map(|p| p.price).sum();
    //     let sum_xy: f64 = self.buffer.iter().enumerate().map(|(x, p)| x as f64 * p.price).sum();
    //     let sum_x2: f64 = (0..self.buffer.len()).map(|x| (x as f64).powi(2)).sum();

    //     let numerator = n * sum_xy - sum_x * sum_y;
    //     let denominator = n * sum_x2 - sum_x * sum_x;
    //     if denominator == 0.0 { return None; }
    //     Some(numerator / denominator)
    // }
}


