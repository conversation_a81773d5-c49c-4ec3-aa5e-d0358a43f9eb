use lapin::types::Void;

// use solana_program::pubkey;
use crate::event::{EventType, SolanaEvent};
use crate::event_bus::{emit_event, init_event_bus};
use borsh::{BorshDeserialize, BorshSerialize};
use deadpool_redis::Pool;
//use solana_sdk::account;
use crate::trade::{burn, swap, TradeType};
use crate::trends::{
    detect_high_trade_rate, get_mint_data, get_trade_rate, increment_trades, list_all_mints,
    remove_mint, update_mint_data, update_price, MintData, PricePoint, PriceWindow, TradeState,
    MINT_DATA_CACHE,
}; // Import the MintData struct and functions
use crate::{common, get_bought, set_bought, trade, BOUGHT}; // Import the common module
use solana_sdk::pubkey::Pubkey;
use std::io::Write;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use {
    futures::{sink::SinkExt, stream::StreamExt},
    maplit::hashmap,
    std::collections::HashMap,
    yellowstone_grpc_client::GeyserGrpcClient,
    yellowstone_grpc_proto::prelude::{
        subscribe_update::UpdateOneof, CommitmentLevel, Message, SubscribeRequest,
        SubscribeRequestFilterBlocksMeta, SubscribeRequestFilterTransactions,
        SubscribeUpdateTransaction, SubscribeUpdateTransactionInfo, Transaction,
    },
};

// use crate::cache::{Cache, get_app_config};
//use super::{BorshDeserialize, BorshSerialize};
const TRADE_ENABLED: bool = false;
pub const SOL_PRICE: f64 = 151.0; // Example price for SOL in USDC
const SOL_BID: f64 = 0.007;
const PROFIT_PCT: f64 = 10.0;
const BUY_TIMEOUT: u64 = 8;
const MAX_SELL_RETRIES: u32 = 5;
const MAX_HOLD_TIME: u64 = 2 * 60 * 60;
static mut CREATE_TOKEN_COUNTER: u64 = 0;
const COIN_CREATOR_VAULT_ATA: &str = "8N3GDaZ2iwN65oxVatKTLPNooAVUJTbfiVJ1ahyqwjSk";
// Target discriminator to match
// const TARGET_DISCRIMINATOR: [u8; 8] = [24, 30, 200, 40, 5, 28, 7, 119];
static mut TOTAL_SPEND_USD: f64 = 0.0;
static mut TOTAL_SOLD_USD: f64 = 0.0;
static mut TOTAL_BOUGHT_NUM: u8 = 0;
static mut TOTAL_SOLD_NUM: u8 = 0;

const BLACKLISTED_CREATOR: &[&str] = &["CU6LrxqZax8wcXw9FCRTNevhkq2LrJoDGzTRSZicPFq6"];

const TARGET_DISCRIMINATOR_PUMPSWAP_CRATE_EVENT: [u8; 8] = [233, 146, 209, 142, 207, 104, 64, 188];
const TARGET_DISCRIMINATOR_PUMPSWAP_BUY: [u8; 8] = [102, 6, 61, 18, 1, 218, 235, 234];
const TARGET_DISCRIMINATOR_PUMPSWAP_SELL: [u8; 8] = [51, 230, 133, 164, 1, 127, 131, 173];
const TARGET_DISCRIMINATOR_PUMPSWAP_WITHDRAW: [u8; 8] = [183, 18, 70, 156, 148, 109, 161, 34];

const TARGET_DISCRIMINATOR_PUMPSWAP_WITHDRAW_EVENT: [u8; 8] = [22, 9, 133, 26, 160, 44, 71, 192];
const TARGET_DISCRIMINATOR_PUMPSWAP_CRATE_EVENT_EVENT: [u8; 8] =
    [177, 49, 12, 210, 160, 118, 167, 116];
const TARGET_DISCRIMINATOR_PUMPSWAP_BUY_EVENT_EVENT: [u8; 8] =
    [103, 244, 82, 31, 44, 245, 119, 119];
const TARGET_DISCRIMINATOR_PUMPSWAP_SELL_EVENT_EVENT: [u8; 8] = [62, 47, 55, 10, 165, 3, 220, 42];
const PMM_ACCOUNT: &str = "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA";

// Delay tracking constants
const DELAY_THRESHOLD_SECONDS: u64 = 3;
const DELAY_RESET_DURATION_SECONDS: u64 = 10;

// Delay tracking structure
#[derive(Debug, Clone)]
struct DelayTracker {
    consecutive_delays_start: Option<Instant>,
    last_delay_time: Option<Instant>,
    total_delay_duration: Duration,
    reset_count: u32,
}

impl DelayTracker {
    fn new() -> Self {
        Self {
            consecutive_delays_start: None,
            last_delay_time: None,
            total_delay_duration: Duration::new(0, 0),
            reset_count: 0,
        }
    }

    fn update_delay(&mut self, delay_seconds: u64) -> bool {
        let now = Instant::now();

        if delay_seconds >= DELAY_THRESHOLD_SECONDS {
            // We have a significant delay
            if self.consecutive_delays_start.is_none() {
                // Start tracking consecutive delays
                self.consecutive_delays_start = Some(now);
                println!("🟡 Started tracking consecutive delays ({}s delay)", delay_seconds);
            }

            self.last_delay_time = Some(now);

            // Check if we've been delayed for too long
            if let Some(start_time) = self.consecutive_delays_start {
                let consecutive_duration = now.duration_since(start_time);
                if consecutive_duration.as_secs() >= DELAY_RESET_DURATION_SECONDS {
                    println!("🔴 Stream has been delayed for {}s, triggering reset", consecutive_duration.as_secs());
                    self.reset_count += 1;
                    self.total_delay_duration += consecutive_duration;
                    self.consecutive_delays_start = None;
                    self.last_delay_time = None;
                    return true; // Signal that reset is needed
                }
            }
        } else {
            // Delay is back to normal
            if let Some(start_time) = self.consecutive_delays_start {
                let delay_duration = now.duration_since(start_time);
                self.total_delay_duration += delay_duration;
                println!("🟢 Stream delay recovered after {}s", delay_duration.as_secs());
            }
            self.consecutive_delays_start = None;
            self.last_delay_time = None;
        }

        false // No reset needed
    }

    fn get_stats(&self) -> (u32, Duration, bool) {
        let is_currently_delayed = self.consecutive_delays_start.is_some();
        (self.reset_count, self.total_delay_duration, is_currently_delayed)
    }

    fn get_current_delay_duration(&self) -> Option<Duration> {
        self.consecutive_delays_start.map(|start| Instant::now().duration_since(start))
    }
}
#[derive(BorshDeserialize, BorshSerialize, Debug)]
pub struct CreatePoolEvent {
    pub timestamp: i64,
    pub index: u16,
    pub creator: Pubkey,
    pub base_mint: Pubkey,
    pub quote_mint: Pubkey,
    pub base_mint_decimals: u8,
    pub quote_mint_decimals: u8,
    pub base_amount_in: u64,
    pub quote_amount_in: u64,
    pub pool_base_amount: u64,
    pub pool_quote_amount: u64,
    pub minimum_liquidity: u64,
    pub initial_liquidity: u64,
    pub lp_token_amount_out: u64,
    pub pool_bump: u8,
    pub pool: Pubkey,
    pub lp_mint: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub coin_creator: Pubkey,
}

#[derive(BorshDeserialize, BorshSerialize, Debug)]
pub struct WithdrawEvent {
    pub timestamp: i64,
    pub lp_token_amount_in: u64,
    pub min_base_amount_out: u64,
    pub min_quote_amount_out: u64,
    pub user_base_token_reserves: u64,
    pub user_quote_token_reserves: u64,
    pub pool_base_token_reserves: u64,
    pub pool_quote_token_reserves: u64,
    pub base_amount_out: u64,
    pub quote_amount_out: u64,
    pub lp_mint_supply: u64,
    pub pool: Pubkey,
    pub user: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub user_pool_token_account: Pubkey,
}
#[derive(BorshDeserialize, BorshSerialize, Debug)]
pub struct BuyEvent {
    pub timestamp: i64,
    pub base_amount_out: u64,
    pub max_quote_amount_in: u64,
    pub user_base_token_reserves: u64,
    pub user_quote_token_reserves: u64,
    pub pool_base_token_reserves: u64,
    pub pool_quote_token_reserves: u64,
    pub quote_amount_in: u64,
    pub lp_fee_basis_points: u64,
    pub lp_fee: u64,
    pub protocol_fee_basis_points: u64,
    pub protocol_fee: u64,
    pub quote_amount_in_with_lp_fee: u64,
    pub user_quote_amount_in: u64,
    pub pool: Pubkey,
    pub user: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub protocol_fee_recipient: Pubkey,
    pub protocol_fee_recipient_token_account: Pubkey,
    pub coin_creator: Pubkey,
    pub coin_creator_fee_basis_points: u64,
    pub coin_creator_fee: u64,
}

#[derive(BorshDeserialize, BorshSerialize, Debug)]
pub struct SellEvent {
    pub timestamp: i64,
    pub base_amount_in: u64,
    pub min_quote_amount_out: u64,
    pub user_base_token_reserves: u64,
    pub user_quote_token_reserves: u64,
    pub pool_base_token_reserves: u64,
    pub pool_quote_token_reserves: u64,
    pub quote_amount_out: u64,
    pub lp_fee_basis_points: u64,
    pub lp_fee: u64,
    pub protocol_fee_basis_points: u64,
    pub protocol_fee: u64,
    pub quote_amount_out_without_lp_fee: u64,
    pub user_quote_amount_out: u64,
    pub pool: Pubkey,
    pub user: Pubkey,
    pub user_base_token_account: Pubkey,
    pub user_quote_token_account: Pubkey,
    pub protocol_fee_recipient: Pubkey,
    pub protocol_fee_recipient_token_account: Pubkey,
    pub coin_creator: Pubkey,
    pub coin_creator_fee_basis_points: u64,
    pub coin_creator_fee: u64,
}
// 8,2,45,45,45,1,1,8,8,8,8,8,8,8,1,45,45,45,45

pub fn get_price(sol_amount: f64, token_amount: f64, sol_decimal: u8, token_decimal: u8) -> f64 {
    // calculate price
    let sol_amount_normalized = sol_amount / 10f64.powi(sol_decimal as i32);
    let token_amount_normalized = token_amount / 10f64.powi(token_decimal as i32);
    if token_amount_normalized == 0.0 {
        return 0.0; // Avoid division by zero
    }
    let price = sol_amount_normalized / token_amount_normalized;
    price
}

fn decode_create_pool_event(
    data: &[u8],
) -> Result<CreatePoolEvent, Box<dyn std::error::Error + Send + Sync>> {
    // Skip the discriminator (first 8 bytes)
    if data.len() < 8 {
        return Err("Data too short".into());
    }

    let event_data = &data[8..];
    let event = CreatePoolEvent::try_from_slice(event_data)?;
    Ok(event)
}

fn decode_buy_event(data: &[u8]) -> Result<BuyEvent, Box<dyn std::error::Error + Send + Sync>> {
    // Skip the discriminator (first 8 bytes)
    if data.len() < 8 {
        return Err("Data too short".into());
    }

    let event_data = &data[8..];
    let event = BuyEvent::try_from_slice(event_data)?;
    Ok(event)
}

fn decode_compute_budget(data: &[u8]) -> Option<(&str, u32)> {
    if data.len() < 2 {
        return None;
    }

    // Check instruction discriminator
    match data[0] {
        // SetComputeUnitLimit instruction (discriminator = 2)
        2 => {
            if data.len() >= 5 {
                let units = u32::from_le_bytes(data[1..5].try_into().unwrap());
                return Some(("unit_limit", units));
            }
        }
        // SetComputeUnitPrice instruction (discriminator = 3)
        3 => {
            if data.len() >= 9 {
                let micro_lamports = u64::from_le_bytes(data[1..9].try_into().unwrap());
                // Convert to u32 for consistency (micro_lamports per compute unit)
                return Some(("unit_price", micro_lamports as u32));
            }
        }
        _ => {}
    }
    None
}

fn decode_sell_event(data: &[u8]) -> Result<SellEvent, Box<dyn std::error::Error + Send + Sync>> {
    // Skip the discriminator (first 8 bytes)
    if data.len() < 8 {
        return Err("Data too short".into());
    }

    let event_data = &data[8..];
    let event = SellEvent::try_from_slice(event_data)?;
    Ok(event)
}

fn decode_withdraw_event(
    data: &[u8],
) -> Result<WithdrawEvent, Box<dyn std::error::Error + Send + Sync>> {
    // Skip the discriminator (first 8 bytes)
    if data.len() < 8 {
        return Err("Data too short".into());
    }

    let event_data = &data[8..];
    let event = WithdrawEvent::try_from_slice(event_data)?;
    Ok(event)
}

async fn process_create_pool_event(
    message: &Message,
    tx: &SubscribeUpdateTransactionInfo,
    tx_data: &SubscribeUpdateTransaction,
    inner_tx: &Transaction,
) {
    // Check if the transaction contains the target discriminator

    // if get_bought() {
    //     return;
    // }
    // if list_all_mints().len() > 0 {
    //     return;
    // }

    for ix in &message.instructions {
        if ix.data.len() >= 8 && ix.data[0..8] == TARGET_DISCRIMINATOR_PUMPSWAP_CRATE_EVENT {
            //println!("Program ID: {}", program_id);
            //println!("Token (Account 1): {}", token);
            //println!("Transaction Slot ID: {}", tx_data.slot);

            if let Some(meta) = &tx.meta {
                for log_entry in &meta.log_messages {
                    // Check if this is a program data log
                    if log_entry.starts_with("Program data: ") {
                        let data_part = &log_entry[14..]; // Skip "Program data: "
                                                          //println!("{:?}",data_part);
                                                          // Decode base64 data
                        if let Ok(decoded_data) = base64::decode(data_part) {
                            // Check if this matches CreatePoolEvent discriminator
                            //println!("{:?}", &decoded_data[0..8]);
                            if decoded_data.len() >= 8
                                && decoded_data[0..8]
                                    == TARGET_DISCRIMINATOR_PUMPSWAP_CRATE_EVENT_EVENT
                            {
                                unsafe {
                                    CREATE_TOKEN_COUNTER += 1;
                                }
                                // Decode the event
                                let dlen = ix.data.len();
                                //  println!("Data length: {}", dlen);
                                //  println!("Data length2: {}", decoded_data.len());
                                //  let ddata: &[u8] = &ix.data;
                                match decode_create_pool_event(&decoded_data) {
                                    Ok(event) => {
                                        let mint_address: String;
                                        let mut mint_data = MintData {
                                            num_of_trades: 1,
                                            scam: false,
                                            mint: event.base_mint,
                                            pool: event.pool,
                                            trade_state: TradeState::None,
                                            base_mint: event.base_mint.to_string(),
                                            quote_mint: event.quote_mint.to_string(),
                                            sol_liquidity: 0.0,
                                            start_price: 0.0,
                                            price: 0.0,
                                            start_price_base: 0.0,
                                            buy_time: None,
                                            sell_time: None,
                                            sell_token_amount: 0.0,
                                            sell_sol_amount: 0.0,
                                            sell_retry_count: 0,
                                            created: std::time::SystemTime::now(),
                                            creator: event.coin_creator,
                                            last_updated: std::time::SystemTime::now(),
                                            volume: 0.0,
                                            market_cap: 0.0,
                                            ath_mcap: 0.0,
                                            sol_reserves: 0.0,
                                            token_reserves: 0.0,
                                            recent_trade_timestamps: Vec::new(),
                                            withdrawn: false, // Default to false
                                            window: PriceWindow::new(),
                                            fee_account: Pubkey::default(),
                                            bought: false,
                                            token_decimals: 0,
                                            sold: false,
                                            buy_price: 0.0,
                                            sell_price: 0.0,
                                            buy_token_amount: 0.0,
                                            tx_counter: 0,
                                            multi_tx: 0,
                                            single_tx: 0,
                                            unit_limit: String::new(),
                                            unit_price: String::new(),
                                            tip_amount: 0.0,
                                            bot_amount: 0,
                                            bot_sol_amount: 0.0,
                                            buy_sol_amount: 0.0, // Initialize the sliding window
                                            unit_occurrences: HashMap::new(), // Initialize the unit occurrences map
                                            max_pct: 0.0,                     // Default to 10.0
                                            enabled: false,                   // Default to true
                                        };
                                        let mut price = 0.0;
                                        let mut market_cup = 0.0;

                                        if BLACKLISTED_CREATOR
                                            .contains(&event.creator.to_string().as_str())
                                        {
                                            mint_data.scam = true;
                                            return;
                                        }

                                        if event.base_mint.to_string()
                                            == "So11111111111111111111111111111111111111112"
                                        {
                                            let sol_amount = event.base_amount_in as f64
                                                / (1_000_000_000) as f64;

                                            mint_data.sol_liquidity = sol_amount;
                                            mint_address = event.quote_mint.to_string();
                                            mint_data.mint = event.quote_mint;

                                            let base_amount = event.base_amount_in as f64
                                                / 10f64.powi(event.base_mint_decimals as i32);
                                            let quote_amount = event.quote_amount_in as f64
                                                / 10f64.powi(event.quote_mint_decimals as i32);
                                            let price = base_amount / quote_amount;

                                            mint_data.token_decimals = event.quote_mint_decimals;
                                            market_cup =
                                                &price * 1_000_000_000 as f64 * SOL_PRICE as f64;
                                            mint_data.sol_reserves = event.base_amount_in as f64;
                                            mint_data.token_reserves = event.quote_amount_in as f64;
                                            mint_data.price = price;
                                            mint_data.market_cap = market_cup;
                                        // Example calculation
                                        } else {
                                            //process only wsol base mints
                                            return;
                                            // let sol_amount = event.quote_amount_in as f64
                                            //     / (1_000_000_000) as f64;

                                            // mint_data.sol_liquidity = sol_amount;
                                            // mint_address = event.base_mint.to_string();
                                            // mint_data.mint = event.base_mint;

                                            // let base_amount = event.base_amount_in as f64
                                            //     / 10f64.powi(event.base_mint_decimals as i32);
                                            // let quote_amount = event.quote_amount_in as f64
                                            //     / 10f64.powi(event.quote_mint_decimals as i32);
                                            // let price = quote_amount / base_amount;
                                            // mint_data.sol_reserves = event.quote_amount_in as f64;
                                            // mint_data.token_reserves = event.base_amount_in as f64;
                                            // market_cup =
                                            //     &price * 1_000_000_000 as f64 * SOL_PRICE as f64;

                                            // mint_data.token_decimals = event.base_mint_decimals;
                                            // mint_data.price = price;
                                            // mint_data.market_cap = market_cup;
                                        }

                                        if mint_data.ath_mcap < market_cup {
                                            mint_data.ath_mcap = market_cup;
                                        }

                                        //let mints = list_all_mints();
                                        //println!("{:?}", &mints);
                                        // for mint_address in &mints {
                                        //     let mint_data = get_mint_data(mint_address);
                                        //     if let Some(data) = mint_data {
                                        //         if let Some(trend) = data.window.get_trend_pct() {
                                        //             println!(
                                        //                 "Mint: {} Trend: {:.2}%",
                                        //                 mint_address, trend
                                        //             );
                                        //         }
                                        //     }
                                        // }

                                        // Terminal URL for the base mint
                                        if event.base_mint.to_string()
                                            == "So11111111111111111111111111111111111111112"
                                        {
                                            let sol_event = SolanaEvent {
                                                event_type: EventType::CreatePool,
                                                mint: Some(mint_address.clone()),
                                                pool: Some(event.pool.to_string()),
                                                sol_liquidity: Some(mint_data.sol_liquidity),
                                                base_mint: Some(event.base_mint.to_string()),
                                                quote_mint: Some(event.quote_mint.to_string()),
                                                base_amount: Some(event.base_amount_in as i64),
                                                quote_amount: Some(event.quote_amount_in as i64),
                                                sol_reserves: Some(mint_data.sol_reserves),
                                                token_reserves: Some(mint_data.token_reserves),
                                                creator: Some(event.creator.to_string()),
                                                market_cap: Some(market_cup),
                                                start_price: Some(price),
                                                start_price_base: Some(price),
                                                dev_entity_id: Some(0),
                                                multi_tx: Some(0),
                                                single_tx: Some(0),
                                                unit_limit: Some("".to_string()),
                                                unit_price: Some("".to_string()),
                                                tip_amount: Some(0.0),
                                                bot_amount: Some(0),
                                                bot_sol_amount: Some(0.0),
                                            };
                                            let start_price_base = get_price(
                                                event.base_amount_in as f64,
                                                event.quote_amount_in as f64,
                                                9,
                                                6,
                                            );
                                            mint_data.start_price_base = start_price_base;

                                            emit_event(sol_event).await;

                                            increment_trades(&mint_address);
                                            println!("num mints: {}", list_all_mints().len());
                                            update_mint_data(&mint_address, mint_data.clone());

                                            // println!(
                                            //     "Mint added: {} {:?}",
                                            //     &mint_address,
                                            //     mint_data.clone()
                                            // );
                                            // println!(
                                            // "POOL size: {} SOL, price:{} SOL, MC:{}$ Terminal URL: https://mevx.io/solana/{}",
                                            // &mint_data.sol_liquidity, &mint_data.price,&mint_data.market_cap, mint_address);
                                        }
                                    }
                                    Err(err) => {
                                        eprintln!("Failed to decode CreatePoolEvent: {}", err);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Skip detailed logging for non-matching transactions
    // if !found_target_discriminator {
    //     continue;
    // }

    Void::default()
}

async fn process_withdraw_event(
    message: &Message,
    tx: &SubscribeUpdateTransactionInfo,
    tx_data: &SubscribeUpdateTransaction,
    inner_tx: &Transaction,
) {
    // Check if the transaction contains the target discriminator

    for ix in &message.instructions {
        if ix.data.len() >= 8 && ix.data[0..8] == TARGET_DISCRIMINATOR_PUMPSWAP_WITHDRAW {
            if let Some(meta) = &tx.meta {
                for log_entry in &meta.log_messages {
                    // Check if this is a program data log
                    if log_entry.starts_with("Program data: ") {
                        let data_part = &log_entry[14..]; // Skip "Program data: "
                                                          //println!("{:?}",data_part);
                                                          // Decode base64 data
                        if let Ok(decoded_data) = base64::decode(data_part) {
                            // Check if this matches CreatePoolEvent discriminator
                            //println!("{:?}", &decoded_data[0..8]);
                            if decoded_data.len() >= 8
                                && decoded_data[0..8]
                                    == TARGET_DISCRIMINATOR_PUMPSWAP_WITHDRAW_EVENT
                            {
                                // Decode the event
                                let dlen = ix.data.len();

                                match decode_withdraw_event(&decoded_data) {
                                    Ok(event) => {
                                        let mint_data =
                                            get_mint_data(event.pool.to_string().as_str());
                                        if let Some(mut data) = mint_data {
                                            data.withdrawn = true;
                                            data.trade_state = TradeState::Withdrawn;
                                            update_mint_data(&event.pool.to_string(), data.clone());
                                            // println!("WITHDRAWN: {:?}", data);
                                            let sol_event = SolanaEvent {
                                                event_type: EventType::Withdraw,
                                                mint: Some(data.mint.to_string()),
                                                pool: Some(event.pool.to_string()),
                                                sol_liquidity: Some(0.0),
                                                base_mint: Some("".to_string()),
                                                quote_mint: Some("".to_string()),
                                                base_amount: Some(event.base_amount_out as i64),
                                                quote_amount: Some(event.quote_amount_out as i64),
                                                sol_reserves: Some(0.0),
                                                token_reserves: Some(0.0),
                                                creator: Some(event.user.to_string()),
                                                market_cap: Some(0.0),
                                                start_price: Some(0.0),
                                                start_price_base: Some(0.0),
                                                dev_entity_id: Some(0),
                                                multi_tx: Some(0),
                                                single_tx: Some(0),
                                                unit_limit: Some("".to_string()),
                                                unit_price: Some("".to_string()),
                                                tip_amount: Some(0.0),
                                                bot_amount: Some(0),
                                                bot_sol_amount: Some(0.0),
                                            };
                                            emit_event(sol_event).await;
                                        }
                                    }
                                    Err(err) => {
                                        eprintln!("Failed to decode Withdraw: {}", err);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Skip detailed logging for non-matching transactions
    // if !found_target_discriminator {
    //     continue;
    // }

    Void::default()
}

async fn process_trade_event(
    message: &Message,
    tx: &SubscribeUpdateTransactionInfo,
    tx_data: &SubscribeUpdateTransaction,
    inner_tx: &Transaction,
) {
    // CHECK if TX is TRADE
    let mut found: bool = false;
    if let Some(meta) = &tx.meta {
        for log_entry in &meta.log_messages {
            // Check if this is a program data log
            if log_entry.starts_with("Program data: ") {
                let data_part = &log_entry[14..]; // Skip "Program data: "
                                                  //println!("{:?}",data_part);
                                                  // Decode base64 data
                if let Ok(decoded_data) = base64::decode(data_part) {
                    // Check if this matches CreatePoolEvent discriminator
                    //println!("{:?}", &decoded_data[0..16]);
                    if decoded_data.len() >= 8
                        && (decoded_data[0..8] == TARGET_DISCRIMINATOR_PUMPSWAP_BUY_EVENT_EVENT
                            || decoded_data[0..8] == TARGET_DISCRIMINATOR_PUMPSWAP_SELL_EVENT_EVENT)
                    {
                        found = true;
                        break;
                    }
                }
            }
        }
    }

    // TRADE NOT FOUND - lets do inner check
    if !found {
        return;
    }

    let mut mint: String = "".to_string();

    // CHECK MINT IN MAIN TX
    for instruction in &inner_tx.message.as_ref().unwrap().instructions {
        if let Some(account_key) = message
            .account_keys
            .get(instruction.program_id_index as usize)
        {
            if bs58::encode(account_key).into_string() == PMM_ACCOUNT
                && instruction.accounts.len() == 19
            {
                //print string of 4th position in accounts
                //println!(" account len: {}", instruction.accounts.len());
                // println!(
                //     "Transaction ID: {}",
                //     bs58::encode(&tx.signature).into_string()
                // );
                if message.account_keys.len() > *instruction.accounts.get(3).unwrap() as usize
                    && message.account_keys.len() > *instruction.accounts.get(4).unwrap() as usize
                {
                    //return;

                    // println!(
                    //     "acclen: {} unwrap 3: {}  unwarp 4 {}",
                    //     message.account_keys.len(),
                    //     *instruction.accounts.get(3).unwrap(),
                    //     *instruction.accounts.get(4).unwrap()
                    // );

                    let mint_address = bs58::encode(
                        message
                            .account_keys
                            .get(*instruction.accounts.get(3).unwrap() as usize)
                            .unwrap(),
                    )
                    .into_string();
                    let quote_address = bs58::encode(
                        message
                            .account_keys
                            .get(*instruction.accounts.get(4).unwrap() as usize)
                            .unwrap(),
                    )
                    .into_string();
                    //println!("Mint Address: {}", mint_address);
                    //println!("Quote Address: {}", quote_address);
                    if mint_address == "So11111111111111111111111111111111111111112".to_string() {
                        mint = quote_address;
                    } else {
                        mint = mint_address;
                    }
                    break;
                }
            }
        }
    }

    // CHECK MINT IN INNER INSTRUCTIONS
    if mint.is_empty() {
        for i_instruction in &tx.meta.as_ref().unwrap().inner_instructions {
            for ii_instruction in &i_instruction.instructions {
                if ii_instruction.accounts.len() == 19 {
                    if message.account_keys.len()
                        > *ii_instruction.accounts.get(16).unwrap() as usize
                        && message.account_keys.len()
                            > *ii_instruction.accounts.get(4).unwrap() as usize
                    {
                        let acc = bs58::encode(
                            message
                                .account_keys
                                .get(*ii_instruction.accounts.get(16).unwrap() as usize)
                                .unwrap(),
                        )
                        .into_string();
                        if acc != PMM_ACCOUNT {
                            continue;
                        }

                        if message.account_keys.len()
                            > *ii_instruction.accounts.get(3).unwrap() as usize
                            && message.account_keys.len()
                                > *ii_instruction.accounts.get(4).unwrap() as usize
                        {
                            let mint_address = bs58::encode(
                                message
                                    .account_keys
                                    .get(*ii_instruction.accounts.get(3).unwrap() as usize)
                                    .unwrap(),
                            )
                            .into_string();

                            let quote_address = bs58::encode(
                                message
                                    .account_keys
                                    .get(*ii_instruction.accounts.get(4).unwrap() as usize)
                                    .unwrap(),
                            )
                            .into_string();

                            //println!("Mint Address: {}", mint_address);
                            //println!("Quote Address: {}", quote_address);
                            if mint_address
                                == "So11111111111111111111111111111111111111112".to_string()
                            {
                                mint = quote_address;
                            } else {
                                mint = mint_address;
                            }
                            // println!(
                            //     "!!!!!!!!Transaction ID: {} 16: {} mint: {}",
                            //     bs58::encode(&tx.signature).into_string(),
                            //     acc,
                            //     &mint
                            // );
                            break;
                        }
                    }
                }
            }
        }
    }

    if mint.is_empty() {
        for ix in &message.instructions {
            if ix.data.len() >= 8 && ix.data[0..8] == TARGET_DISCRIMINATOR_PUMPSWAP_BUY
                || ix.data.len() >= 8 && ix.data[0..8] == TARGET_DISCRIMINATOR_PUMPSWAP_SELL
            {
                let mints = list_all_mints();

                for account in &message.account_keys {
                    if mints.contains(&bs58::encode(account).into_string()) {
                        mint = bs58::encode(account).into_string();
                        break;

                        //println!("Mint found in mints list: {}", bs58::encode(account).into_string());
                        // increment_trades(&bs58::encode(account).into_string());
                        // if mints.len()>0 {
                        //     println!("Mints: {:?}", &bs58::encode(account).into_string());
                        // }

                        // if detect_high_trade_rate(
                        //     &bs58::encode(account).into_string(),
                        //     detect_trade_treshold,
                        // ) {
                        //     let trade_rate = get_trade_rate(&bs58::encode(account).into_string());
                        //     println!(
                        //         "High trade rate detected for mint: {} t/s: {}",
                        //         bs58::encode(account).into_string(),
                        //         trade_rate.unwrap()
                        //     );
                        //     // Here you can add additional logic, like sending alerts or logging
                        // }

                        // if let Some(ix_data) = message.instructions.get(0) {
                        //     if ix_data.data.len() >= 16 {
                        //         let base_amount =
                        //             u64::from_le_bytes(ix_data.data[8..16].try_into().unwrap());
                        //         let quote_amount =
                        //             u64::from_le_bytes(ix_data.data[16..24].try_into().unwrap());

                        //         println!(
                        //             "Base amount: {}, Quote amount: {}",
                        //             base_amount, quote_amount
                        //         );

                        //         if quote_amount > 0 {
                        //             let trade_price = base_amount as f64 / quote_amount as f64;
                        //             // println!(
                        //             //     "Trade price for mint {}: {:.8}",
                        //             //     bs58::encode(account).into_string(),
                        //             //     trade_price
                        //             // );

                        //             // Update mint data with the calculated trade price
                        //             update_price(&bs58::encode(account).into_string(), trade_price);
                        //             println!(
                        //                 "Trade price for mint {}: {:.8} USDC",
                        //                 bs58::encode(account).into_string(),
                        //                 trade_price
                        //             );
                        //             let mint_data =
                        //                 get_mint_data(&bs58::encode(account).into_string());
                        //             if let Some(mut data) = mint_data {
                        //                 data.price = trade_price;
                        //                 if ! data.bought {
                        //                     let buy_sol = 0.1; // Example value, adjust as needed
                        //                     data.bought = true;
                        //                     data.buy_price = trade_price;
                        //                     data.buy_token_amount = buy_sol * trade_price;
                        //                     data.buy_sol_amount = buy_sol;
                        //                     println!("Bought {} SOL for {} tokens at price {:.8} USDC",
                        //                         buy_sol, &bs58::encode(account).into_string(), trade_price);
                        //                 }
                        //                 data.last_updated = std::time::SystemTime::now();
                        //                 update_mint_data(
                        //                     &bs58::encode(account).into_string(),
                        //                     data.clone(),
                        //                 );
                        //             }
                        //         }
                        //     }
                        // }
                    }
                    // get trade data,
                }
            }

            // start big

            // end big
        }
    }
    // RETURN IF MINT EMPTY
    if mint.is_empty() {
        return;
    }

    //println!("Trade found for mint: ", mint.clone());

    if let Some(mut mint_data) = get_mint_data(&mint) {
        let mut tx_counter = 0;
        if let Some(meta) = &tx.meta {
            for log_entry in &meta.log_messages {
                if log_entry.starts_with("Program data: ") {
                    //println!("Log entry: {}", log_entry);

                    let data_part = &log_entry[14..];
                    if let Ok(decoded_data) = base64::decode(data_part) {
                        if decoded_data.len() >= 8
                            && (decoded_data[0..8] == TARGET_DISCRIMINATOR_PUMPSWAP_BUY_EVENT_EVENT
                                || decoded_data[0..8]
                                    == TARGET_DISCRIMINATOR_PUMPSWAP_SELL_EVENT_EVENT)
                        {
                            tx_counter += 1;
                            let trade_type: String;
                            if decoded_data[0..8] == TARGET_DISCRIMINATOR_PUMPSWAP_SELL_EVENT_EVENT
                            {
                                trade_type = "sell".to_string();
                            } else {
                                trade_type = "buy".to_string();
                            }

                            // Decode the event
                            let token_decimals = if mint_data.token_decimals > 0 {
                                mint_data.token_decimals
                            } else {
                                6 // Default to 6 decimals if not set
                            };

                            let mut price: f64 = 0.0;
                            let sol_amount: f64;
                            let token_amount: f64;
                            let sol_reservers: f64;
                            let token_reserves: f64;
                            let mut trade_op: TradeType = TradeType::Buy;
                            match decode_buy_event(&decoded_data) {
                                Ok(event) => {
                                    if mint_data.scam {
                                        continue;
                                    }

                                    if trade_type == "buy".to_string() {
                                        //if event.pool_base_token_reserves < event.pool_quote_token_reserves
                                        if mint_data.base_mint
                                            == "So11111111111111111111111111111111111111112"
                                        {
                                            price = get_price(
                                                event.pool_base_token_reserves as f64,
                                                event.pool_quote_token_reserves as f64,
                                                9,
                                                token_decimals,
                                            );
                                            sol_amount =
                                                event.base_amount_out as f64 / 10f64.powi(9 as i32);
                                            token_amount = event.user_quote_amount_in as f64
                                                / 10f64.powi(token_decimals as i32);
                                            sol_reservers = event.pool_base_token_reserves as f64;
                                            token_reserves = event.pool_quote_token_reserves as f64;
                                            trade_op = TradeType::Sell;
                                        } else {
                                            price = get_price(
                                                event.pool_quote_token_reserves as f64,
                                                event.pool_base_token_reserves as f64,
                                                9,
                                                token_decimals,
                                            );
                                            sol_amount = event.user_quote_amount_in as f64
                                                / 10f64.powi(9 as i32);
                                            token_amount = event.base_amount_out as f64
                                                / 10f64.powi(token_decimals as i32);
                                            sol_reservers = event.pool_quote_token_reserves as f64;
                                            token_reserves = event.pool_base_token_reserves as f64;
                                            trade_op = TradeType::Buy;
                                        }
                                    } else {
                                        //if event.pool_base_token_reserves> event.pool_quote_token_reserves
                                        if mint_data.base_mint
                                            != "So11111111111111111111111111111111111111112"
                                        {
                                            price = get_price(
                                                event.pool_quote_token_reserves as f64,
                                                event.pool_base_token_reserves as f64,
                                                9,
                                                token_decimals,
                                            );
                                            sol_amount = event.user_quote_amount_in as f64
                                                / 10f64.powi(9 as i32);
                                            token_amount = event.base_amount_out as f64
                                                / 10f64.powi(token_decimals as i32);
                                            sol_reservers = event.pool_quote_token_reserves as f64;
                                            token_reserves = event.pool_base_token_reserves as f64;
                                            trade_op = TradeType::Sell;
                                        } else {
                                            price = get_price(
                                                event.pool_base_token_reserves as f64,
                                                event.pool_quote_token_reserves as f64,
                                                9,
                                                token_decimals,
                                            );
                                            sol_amount =
                                                event.base_amount_out as f64 / 10f64.powi(9 as i32);
                                            token_amount = event.user_quote_amount_in as f64
                                                / 10f64.powi(token_decimals as i32);
                                            sol_reservers = event.pool_base_token_reserves as f64;
                                            token_reserves = event.pool_quote_token_reserves as f64;
                                            trade_op = TradeType::Buy;
                                        }
                                    }

                                    let user = event.user.to_string();

                                    let mut pp = PricePoint {
                                        timestamp: event.timestamp as u64,
                                        price,
                                        buy_amount_usd: 0.0,
                                        sell_amount_usd: 0.0,
                                        is_dev: true, // Set to true if this is a dev trade
                                    };

                                    if trade_op == TradeType::Buy {
                                        pp.buy_amount_usd = sol_amount * SOL_PRICE;
                                        pp.sell_amount_usd = 0.0;
                                    } else {
                                        pp.buy_amount_usd = 0.0;
                                        pp.sell_amount_usd = sol_amount * SOL_PRICE;
                                    }

                                    let buy_sol = SOL_BID; // Example value, adjust as needed
                                    mint_data.price = price;
                                    if mint_data.trade_state == TradeState::None {
                                        mint_data.trade_state = TradeState::Monitor;
                                        mint_data.start_price = price;
                                    }
                                    if user == "CRHTUtpsVxkqHeTRdfCVYTrLH9dy23UVG2sVFwJyGDdr" {
                                        //println!(
                                        //    "Account detected,trade op: {:?}  mint data:  {:?}",
                                        //    trade_op, mint_data
                                        //);
                                        if trade_op == TradeType::Buy {
                                            mint_data.trade_state = TradeState::Bought;
                                            mint_data.buy_time = Some(std::time::SystemTime::now());
                                            mint_data.buy_price = price;
                                            mint_data.buy_token_amount =
                                                mint_data.buy_token_amount + token_amount;
                                            mint_data.buy_sol_amount =
                                                mint_data.buy_sol_amount + buy_sol;
                                            mint_data.market_cap =
                                                price * 1_000_000_000 as f64 * SOL_PRICE;

                                            println!("Detected: Bought {} tokens for {} SOL  for {} mint at price {:.8} USDC",
                                                        token_amount, buy_sol, &bs58::encode(&mint).into_string(), price);
                                        } else {
                                            mint_data.trade_state = TradeState::Sold;
                                            mint_data.sell_time =
                                                Some(std::time::SystemTime::now());
                                            mint_data.sell_price = price;
                                            mint_data.sell_token_amount =
                                                mint_data.sell_token_amount + token_amount;
                                            mint_data.sell_sol_amount =
                                                mint_data.sell_sol_amount + sol_amount;
                                            println!("Detected: Sold {} tokens for {} SOL for mint {} at price {:.8} USDC",
                                                        token_amount, sol_amount, &bs58::encode(&mint).into_string(), price);
                                        }
                                    }

                                    if mint_data.ath_mcap < price * 1_000_000_000 as f64 * SOL_PRICE
                                    {
                                        mint_data.ath_mcap =
                                            price * 1_000_000_000 as f64 * SOL_PRICE;
                                    }

                                    mint_data.volume = mint_data.volume + (sol_amount * SOL_PRICE);
                                    mint_data.market_cap = price * 1_000_000_000 as f64 * SOL_PRICE;
                                    mint_data.fee_account = event.protocol_fee_recipient;
                                    mint_data.sol_reserves = sol_reservers;
                                    mint_data.token_reserves = token_reserves;
                                    mint_data.creator = event.coin_creator;

                                    mint_data.window.push(pp.clone());
                                    update_mint_data(&mint, mint_data.clone());
                                }
                                //mint_data.window::push(pp);
                                Err(err) => {
                                    eprintln!("Failed to decode CreatePoolEvent: {}", err);
                                }
                            }

                            //PricePoint
                        }
                    }
                }
            }
        }

        // check if there is ComputeBudget111111111111111111111111111111 program extract data and decode
        if let Some(transaction) = &tx.transaction {
            if let Some(message) = &transaction.message {
                for instruction in &message.instructions {
                    if let Some(account_key) = message
                        .account_keys
                        .get(instruction.program_id_index as usize)
                    {
                        if bs58::encode(account_key).into_string()
                            == "ComputeBudget111111111111111111111111111111"
                        {
                            if let Some((key, value)) = decode_compute_budget(&instruction.data) {
                                if key == "unit_limit" {
                                    //mint_data.unit_limit = value.to_string();

                                    if mint_data.unit_occurrences.contains_key(&value) {
                                        let count = mint_data.unit_occurrences.get(&value).unwrap();
                                        mint_data.unit_occurrences.insert(value, count + 1);
                                    } else {
                                        mint_data.unit_occurrences.insert(value, 1);
                                    }
                                }
                                // else if key == "unit_price" {
                                //     //mint_data.unit_price = value.to_string();

                                //     if mint_data.unit_occurrences.contains_key(&value) {
                                //         let count = mint_data.unit_occurrences.get(&value).unwrap();
                                //         mint_data.unit_occurrences.insert(value, count + 1);
                                //     } else {
                                //         mint_data.unit_occurrences.insert(value, 1);
                                //     }
                                // }
                            }
                        }
                    }
                }
            }
        }

        if tx_counter == 1 {
            mint_data.single_tx = mint_data.single_tx + 1;
        } else if tx_counter > 1 {
            mint_data.multi_tx = mint_data.multi_tx + 1;
        }

        mint_data.tx_counter = tx_counter;
        mint_data.num_of_trades = &mint_data.num_of_trades + 1;
        update_mint_data(&mint, mint_data.clone());
    }
    Void::default()
}

// perform buy!
// buy check timeouts
// sell on pct
// sell on time
// sell retries
// set trade_state:
//

async fn reset_mint(burn_token: bool, mint: String, mut mint_data: MintData) {
    let all_mints = list_all_mints();
    //println!("all tokens Before: {:?}", &all_mints);
    mint_data.trade_state = TradeState::Burned;
    update_mint_data(&mint, mint_data.clone());
    if burn_token {
        if TRADE_ENABLED {
        burn(mint.clone()).await;
        }
    }

    //remove all mints
    // for mint in all_mints.clone() {
    //     remove_mint(mint.as_str());
    // }
    //remove_mint(mint.as_str());

    //set_bought(false);
    //println!("all tokens after: {:?}", list_all_mints());

    set_bought(false);
}

async fn trade_manager(pool: &Pool) {
    let all_mints = list_all_mints();
    //const PROFIT_PCT: f64 = 20.0;
    for mint in all_mints.clone() {
        if let Some(mut mint_data) = get_mint_data(mint.as_str()) {
            match mint_data.trade_state {
                TradeState::None => {}
                TradeState::Monitor => {
                    // Calculate trade rate from recent timestamps
                    let current_time = std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs();

                    // Filter timestamps within last second
                    let recent_trades = mint_data
                        .window
                        .buffer
                        .iter()
                        .filter(|pp| current_time - (pp.timestamp) <= 3)
                        .collect::<Vec<_>>();

                    // Calculate volume in last second
                    let volume = mint_data
                        .window
                        .buffer
                        .iter()
                        .map(|pp| pp.buy_amount_usd + pp.sell_amount_usd)
                        .sum::<f64>();
                    // print latest value from window buffer
                    if let Some(last_price) = mint_data.window.buffer.back() {
                        //print!("\rLast price: {:.8} USDC", last_price.buy_amount_usd);
                        //std::io::stdout().flush().unwrap();

                        // Percentage increase between first buy and last price
                        let price_diff = ((mint_data.price - mint_data.start_price)
                            / mint_data.start_price)
                            * 100.0;

                        /*
                           Enable buy
                        */
                        //TODO: enable buy logic !!!! for example get redis data see if enabled
                        // Get redis connection from pool
                        // let mut conn = crate::redis_pool::get_connection().await.unwrap();

                       // if mint_data.enabled {
                            mint_data.trade_state = TradeState::Ready;
                            update_mint_data(&mint, mint_data.clone());
                       // } else {
                            //println!("Mint not enabled: {}", mint);
                        //}

                       
                        std::io::stdout().flush().unwrap();
                    }
                    // Check trade rate and volume conditions
                }
                TradeState::Cancelled => {}
                TradeState::Ready => {
                    // if !get_bought() {
                    let is_base_mint_wsol =
                        mint_data.base_mint == "So11111111111111111111111111111111111111112";
                    if is_base_mint_wsol {
                        let amount_in: u64 = (SOL_BID * 1_000_000_000.0) as u64; // Convert SOL to lamports
                        let amount_out: u64 =
                            ((SOL_BID / mint_data.price) * 1_000_000.0 / 2.0) as u64; // Convert token amount to lamports
                     
                        if TRADE_ENABLED {
                            println!(
                                "Initiate Swapping {} SOL for {} tokens for mint \x1b[36mhttps://mevx.io/solana/{}\x1b[0m",
                                SOL_BID, amount_out, &mint.clone()
                            );
                        }else {
                            println!(
                                "Simulation Swapping {} SOL for {} tokens for mint \x1b[36mhttps://mevx.io/solana/{}\x1b[0m",
                                SOL_BID, amount_out, &mint.clone()
                            );

                        }
                        


                        mint_data.buy_time = Some(std::time::SystemTime::now());

                        println!("Mint data: {:?}", mint_data);

                        let base_token_price = if mint_data.token_reserves > 0.0 {
                            mint_data.sol_reserves / mint_data.token_reserves / 1000.0
                        } else {
                            0.0
                        };

                        let price_increase =
                            ((mint_data.price - base_token_price) / base_token_price) * 100.0;

                        println!(
                            "Price increase: {} base_token_price; {}  , price: {} ",
                            price_increase, base_token_price, mint_data.price
                        );
                        //  mint_data.trade_state = TradeState::Unknown;
                        // update_mint_data(&mint, mint_data.clone());
                        if TRADE_ENABLED {
                            swap(
                                TradeType::Buy,
                                mint.clone(),
                                mint_data.pool.to_string(),
                                mint_data.creator.to_string(),
                                amount_in,
                                amount_out,
                                mint_data.fee_account,
                                is_base_mint_wsol,
                            )
                            .await;
                         mint_data.trade_state = TradeState::Buying;
                        }else{
                            mint_data.trade_state = TradeState::Cancelled;
                        }

                        
                        update_mint_data(&mint, mint_data.clone());
                    }
                    //}
                }
                TradeState::Buying => {
                    // Check if buy has been in progress too long if over 30 sec set to BuyFailed
                    if let Some(buy_time) = mint_data.buy_time {
                        if let Ok(elapsed) = std::time::SystemTime::now().duration_since(buy_time) {
                            if elapsed.as_secs() > BUY_TIMEOUT {
                                mint_data.trade_state = TradeState::BuyFailed;
                                update_mint_data(&mint, mint_data.clone());
                            }
                        }
                    }
                }
                TradeState::Bought => {
                    let price_increase = ((mint_data.price - mint_data.start_price_base)
                        / mint_data.start_price_base)
                        * 100.0;
                    let elapsed_time =
                        mint_data
                            .buy_time
                            .map_or("unknown".to_string(), |buy_time| {
                                buy_time.elapsed().map_or("error".to_string(), |elapsed| {
                                    format!("{}s", elapsed.as_secs())
                                })
                            });

                    // println!("Token: {} Amount: {:.8}  Start price: {:.8} Price: {:.8}  Increase: {:.2}% Target: {:.2}% Elapsed: {}",
                    //     &bs58::encode(&mint).into_string(),
                    //     mint_data.buy_token_amount,
                    //      mint_data.start_price_base,
                    //     mint_data.price,
                    //     price_increase,
                    //     mint_data.max_pct,
                    //     elapsed_time
                    // );

                    //std::io::stdout().flush().unwrap();

                    let mut max_pct: f64 = 50.0;
                    if mint_data.max_pct < max_pct {
                        max_pct = mint_data.max_pct;
                    }

                    if mint_data.buy_token_amount > 0.0
                        && (price_increase > max_pct
                            || mint_data.buy_time.map_or(false, |buy_time| {
                                buy_time
                                    .elapsed()
                                    .map_or(false, |elapsed| elapsed.as_secs() > MAX_HOLD_TIME)
                            }))
                    {
                        println!(
                            ">>>>>>>selling: {} tokens for {} SOL  for {} mint at price {:.8} USDC",
                            mint_data.buy_token_amount,
                            SOL_BID,
                            &bs58::encode(&mint).into_string(),
                            mint_data.price
                        );

                        if mint_data.base_mint == "So11111111111111111111111111111111111111112" {
                            let selling_price = mint_data.price - (mint_data.price * 0.02); // Calculate price minus 2%
                            mint_data.trade_state = TradeState::Selling;
                            mint_data.sell_time = Some(std::time::SystemTime::now());
                            swap(
                                TradeType::Sell,
                                mint.clone(),
                                mint_data.pool.to_string(),
                                mint_data.creator.to_string(),
                                (mint_data.buy_token_amount * 1000000.0) as u64,
                                (mint_data.buy_token_amount * selling_price * 1_000_000_000.0)
                                    as u64,
                                mint_data.fee_account,
                                true,
                            )
                            .await;
                        }
                    }
                    update_mint_data(&mint, mint_data.clone());
                }
                TradeState::BuyFailed => {
                    // Handle buy failed state. Remove token from minst list
                    println!("Buy failed for mint: {} removing!!", mint);
                    reset_mint(false, mint.clone(), mint_data.clone()).await;
                    //remove_mint(mint.as_str());
                    //set_bought(false);
                    mint_data.trade_state = TradeState::Cancelled;
                }
                TradeState::Selling => {
                    if let Some(sell_time) = mint_data.sell_time {
                        if let Ok(elapsed) = std::time::SystemTime::now().duration_since(sell_time)
                        {
                            if elapsed.as_secs() > 2 {
                                mint_data.sell_retry_count += 1;
                                if mint_data.sell_retry_count > MAX_SELL_RETRIES {
                                    mint_data.trade_state = TradeState::SellFailed;
                                    update_mint_data(&mint, mint_data.clone());
                                    continue;
                                }
                                println!(">>>>>>>Retrying sell: {} tokens for {} SOL  for {} mint at price {:.8} USDC",
                                mint_data.buy_token_amount, SOL_BID, &bs58::encode(&mint).into_string(), mint_data.price);

                                if mint_data.base_mint
                                    == "So11111111111111111111111111111111111111112"
                                {
                                    let selling_price = mint_data.price - (mint_data.price * 0.05); // Calculate price minus 2%
                                    mint_data.trade_state = TradeState::Selling;
                                    mint_data.sell_time = Some(std::time::SystemTime::now());
                                    swap(
                                        TradeType::Sell,
                                        mint.clone(),
                                        mint_data.pool.to_string(),
                                        mint_data.creator.to_string(),
                                        (mint_data.buy_token_amount * 1000000.0) as u64,
                                        (mint_data.buy_token_amount
                                            * selling_price
                                            * 1_000_000_000.0)
                                            as u64,
                                        mint_data.fee_account,
                                        true,
                                    )
                                    .await;
                                }
                            }
                        }
                    }
                }
                TradeState::Sold => {
                    if let Some(sell_time) = mint_data.sell_time {
                        if let Ok(elapsed) = std::time::SystemTime::now().duration_since(sell_time)
                        {
                            if elapsed.as_secs() > 10 {
                                reset_mint(true, mint.clone(), mint_data.clone()).await;
                                continue;
                            }
                        }
                    }
                }
                TradeState::SellFailed => {
                    // sorry burning
                    reset_mint(true, mint.clone(), mint_data.clone()).await;
                    mint_data.trade_state = TradeState::Cancelled;
                    update_mint_data(&mint, mint_data.clone());
                }
                TradeState::Burning => {
                    // Handle burning state
                }
                TradeState::Burned => {
                    // Handle burned state
                }
                TradeState::Error => {
                    // Handle error state
                }
                TradeState::BuyTimedOut => {}
                TradeState::SellTimedOut => {}
                TradeState::Unknown => {}
                TradeState::Withdrawn => {
                    // println!("WITHDRAWN: {}", &mint);
                    reset_mint(true, mint.clone(), mint_data.clone()).await;

                    mint_data.trade_state = TradeState::Burned;
                    update_mint_data(&mint, mint_data.clone());
                }
            }

            // if mint_data.bought && !mint_data.sold && !mint_data.withdrawn {
            //     let current_price = mint_data.price;
            //     let buy_price = mint_data.buy_price;

            //     let profit = ((current_price - buy_price) / buy_price) * 100.0;

            //     if profit > PROFIT_PCT {
            //         let mut updated_mint_data = mint_data.clone();

            //         //update_mint_data(mint.as_str(), updated_mint_data);
            //     } else {
            //         //pecentage not over 20 lets checktime
            //         let current_time = std::time::SystemTime::now();
            //         if let Ok(elapsed) = current_time.duration_since(mint_data.created) {
            //             if elapsed.as_secs() > 60 {
            //                 let mut updated_mint_data = mint_data.clone();

            //                 //update_mint_data(mint.as_str(), updated_mint_data);
            //             }
            //         }
            //     }
            // }
            update_mint_data(&mint, mint_data.clone());
        }
    }
}

// async fn trade_manager_simulation() {
//     let all_mints = list_all_mints();
//     //const PROFIT_PCT: f64 = 20.0;
//     for mint in all_mints.clone() {
//         if let Some(mint_data) = get_mint_data(mint.as_str()) {
//             if mint_data.bought && !mint_data.sold && !mint_data.withdrawn {
//                 let current_price = mint_data.price;
//                 let buy_price = mint_data.buy_price;
//                 let profit = ((current_price - buy_price) / buy_price) * 100.0;

//                 if profit > PROFIT_PCT {
//                     let mut updated_mint_data = mint_data.clone();
//                     updated_mint_data.sold = true;
//                     updated_mint_data.sell_price = current_price;

//                     let profit_usd = ((SOL_BID + (SOL_BID * profit / (100.0 as f64))) * SOL_PRICE)
//                         - (SOL_BID * SOL_PRICE);

//                     unsafe {
//                         TOTAL_SOLD_USD = TOTAL_SOLD_USD
//                             + ((SOL_BID + (SOL_BID * profit / (100.0 as f64))) * SOL_PRICE);
//                         TOTAL_SOLD_NUM = TOTAL_SOLD_NUM + 1;
//                     }

//                     unsafe {
//                         println!(
//                             "====> pct Selling {} ({}/{})  SOL profit: {:.0}%  {:.4}$   B:{} S:{}  BoughtUSD:{:.2}$ SoldUSD:{:.2}$",
//                             mint, current_price,buy_price, profit,profit_usd,TOTAL_BOUGHT_NUM,TOTAL_SOLD_NUM,TOTAL_SPEND_USD,TOTAL_SOLD_USD
//                         );
//                     }

//                     update_mint_data(mint.as_str(), updated_mint_data);
//                 } else {
//                     //pecentage not over 20 lets checktime
//                     let current_time = std::time::SystemTime::now();
//                     if let Ok(elapsed) = current_time.duration_since(mint_data.created) {
//                         if elapsed.as_secs() > 60 {
//                             let mut updated_mint_data = mint_data.clone();
//                             updated_mint_data.sold = true;
//                             updated_mint_data.sell_price = current_price;
//                             let profit_usd = ((SOL_BID + (SOL_BID * profit / (100.0 as f64)))
//                                 * SOL_PRICE)
//                                 - (SOL_BID * SOL_PRICE);
//                             unsafe {
//                                 TOTAL_SOLD_USD = TOTAL_SOLD_USD
//                                     + ((SOL_BID + (SOL_BID * profit / (100.0 as f64))) * SOL_PRICE);
//                                 TOTAL_SOLD_NUM = TOTAL_SOLD_NUM + 1;
//                             }
//                             unsafe {
//                                 println!(
//                                     "====> Timeout Selling {} ({}/{})  SOL profit: {:.0}%  {:.4}$   B:{} S:{}  BoughtUSD:{:.2}$ SoldUSD:{:.2}$",
//                                     mint, current_price,buy_price, profit,profit_usd,TOTAL_BOUGHT_NUM,TOTAL_SOLD_NUM,TOTAL_SPEND_USD,TOTAL_SOLD_USD
//                                 );
//                             }

//                             update_mint_data(mint.as_str(), updated_mint_data);
//                         }
//                     }
//                 }
//             }
//         }
//     }
// }

#[allow(unused_variables)]
#[allow(unused_assignments)]
pub async fn grpc(
    cache: Arc<Mutex<HashMap<String, HashMap<String, String>>>>,
    pool: Pool, // Accept the Redis connection pool
) -> anyhow::Result<()> {
    // Initialize delay tracker
    let mut delay_tracker = DelayTracker::new();
    let mut reconnect_attempts = 0;
    const MAX_RECONNECT_ATTEMPTS: u32 = 5;

    loop {
        match grpc_stream_with_reset(&cache, &pool, &mut delay_tracker).await {
            Ok(_) => {
                println!("🟢 gRPC stream completed normally");
                break;
            }
            Err(e) => {
                reconnect_attempts += 1;
                let (reset_count, total_delay, is_delayed) = delay_tracker.get_stats();

                println!(
                    "🔴 gRPC stream error (attempt {}/{}): {:?}",
                    reconnect_attempts, MAX_RECONNECT_ATTEMPTS, e
                );
                println!(
                    "📊 Delay stats - Resets: {}, Total delay: {}s, Currently delayed: {}",
                    reset_count, total_delay.as_secs(), is_delayed
                );

                if reconnect_attempts >= MAX_RECONNECT_ATTEMPTS {
                    return Err(anyhow::anyhow!(
                        "Max reconnection attempts ({}) exceeded. Last error: {:?}",
                        MAX_RECONNECT_ATTEMPTS, e
                    ));
                }

                // Exponential backoff for reconnection
                let backoff_duration = Duration::from_secs(2_u64.pow(reconnect_attempts.min(6)));
                println!("⏳ Waiting {}s before reconnection attempt...", backoff_duration.as_secs());
                tokio::time::sleep(backoff_duration).await;
            }
        }
    }

    Ok(())
}

async fn grpc_stream_with_reset(
    cache: &Arc<Mutex<HashMap<String, HashMap<String, String>>>>,
    pool: &Pool,
    delay_tracker: &mut DelayTracker,
) -> anyhow::Result<()> {
    // Default configuration values
    let endpoint = "http://grpc.solanavibestation.com:10000";
    let commitment = CommitmentLevel::Processed;
    let vote = Some(false);
    let failed = Some(false);
    let signature = None;
    let account_include: Vec<String> = vec![
        "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA".to_string(),
        "68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je".to_string(),
    ];
    let account_exclude: Vec<String> = vec![];
    let account_required: Vec<String> = vec![];

    // Connect to the gRPC endpoint
    println!("🔄 Connecting to Yellowstone gRPC at {}", endpoint);
    let mut client = GeyserGrpcClient::build_from_shared(endpoint)?
        .connect()
        .await?;

    // Create subscription stream
    let (mut subscribe_tx, mut stream) = client.subscribe().await?;

    // Send subscription request
    subscribe_tx
        .send(SubscribeRequest {
            slots: HashMap::new(),
            accounts: HashMap::new(),
            transactions: hashmap! { "".to_owned() => SubscribeRequestFilterTransactions {
                vote,
                failed,
                signature: signature.clone(),
                account_include: account_include.clone(),
                account_exclude: account_exclude.clone(),
                account_required: account_required.clone(),
            } },
            transactions_status: hashmap! { "".to_owned() => SubscribeRequestFilterTransactions {
                vote,
                failed,
                signature: signature.clone(),
                account_include: account_include.clone(),
                account_exclude: account_exclude.clone(),
                account_required: account_required.clone(),
            } },
            entry: HashMap::new(),
            blocks: HashMap::new(),
            blocks_meta: hashmap! { "".to_owned() => SubscribeRequestFilterBlocksMeta {} },
            commitment: Some(commitment as i32),
            accounts_data_slice: vec![],
            ping: None,
            from_slot: None,
        })
        .await?;

    println!("✅ Subscription request sent, waiting for transaction updates...");

    // Add periodic status reporting
    let mut last_status_report = Instant::now();
    const STATUS_REPORT_INTERVAL: Duration = Duration::from_secs(30);

    // Process the stream updates
    while let Some(message) = stream.next().await {
        match message {
            Ok(msg) => {
                match msg.update_oneof {
                    Some(UpdateOneof::BlockMeta(block_meta)) => {
                        let blockhash = block_meta.blockhash.clone();
                        let blocktime: u64 = block_meta
                            .block_time
                            .map(|t| t.timestamp as u64)
                            .unwrap_or(0);
                        let current_timestamp = std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap()
                            .as_secs();

                        if blocktime > 0 {
                            let time_diff = current_timestamp - blocktime;

                            // Use delay tracker to monitor and potentially reset stream
                            if delay_tracker.update_delay(time_diff) {
                                println!("🔄 Resetting stream due to persistent delays...");
                                return Err(anyhow::anyhow!("Stream reset triggered by delay tracker"));
                            }

                            // Log significant delays
                            if time_diff > DELAY_THRESHOLD_SECONDS {
                                println!(
                                    "⚠️  Stream delay detected: {}s behind (slot: {})",
                                    time_diff, block_meta.slot
                                );
                            }
                        }

                        if !blockhash.is_empty() {
                            // Update the global blockhash using common module
                            common::update_blockhash(blockhash.clone());
                        }

                        // Periodic status reporting
                        let now = Instant::now();
                        if now.duration_since(last_status_report) >= STATUS_REPORT_INTERVAL {
                            let (reset_count, total_delay, is_delayed) = delay_tracker.get_stats();
                            let current_delay = delay_tracker.get_current_delay_duration();

                            if is_delayed {
                                if let Some(delay_duration) = current_delay {
                                    println!(
                                        "📊 Status: Currently delayed {}s (Total resets: {}, Total delay time: {}s)",
                                        delay_duration.as_secs(), reset_count, total_delay.as_secs()
                                    );
                                }
                            } else if reset_count > 0 || total_delay.as_secs() > 0 {
                                println!(
                                    "📊 Status: Stream healthy (Total resets: {}, Total delay time: {}s)",
                                    reset_count, total_delay.as_secs()
                                );
                            }

                            last_status_report = now;
                        }
                    }

                    Some(UpdateOneof::Transaction(tx_data)) => {
                        if let Some(ref tx) = tx_data.transaction {
                            if let Some(ref inner_tx) = tx.transaction {
                                if let Some(ref message) = inner_tx.message {
                                    // Process transaction events
                                    process_trade_event(&message, &tx, &tx_data, &inner_tx).await;
                                    process_create_pool_event(&message, &tx, &tx_data, &inner_tx)
                                        .await;
                                    process_withdraw_event(&message, &tx, &tx_data, &inner_tx)
                                        .await;
                                    trade_manager(&pool).await;
                                }
                            }
                        }
                    }
                    _ => {}
                }
            }
            Err(error) => {
                println!("🔴 Stream error: {:?}", error);
                return Err(anyhow::anyhow!("gRPC stream error: {:?}", error));
            }
        }
    }

    println!("🔚 gRPC stream ended normally");
    Ok(())
}
