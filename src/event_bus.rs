use once_cell::sync::Lazy;
use tokio::sync::{mpsc, Mutex};
use tokio::task;
use crate::event::EventType;
use crate::db::{
    create_dev_entity, create_dev_token_creation, create_dev_wallet, get_dev_entity, get_dev_token_creation, get_dev_wallet, update_dev_entity_field, update_dev_token_creation, update_dev_wallet, DevEntity, DevTokenCreation, DevWallet
};

use crate::trends::{get_mint_data, list_all_mints, update_mint_data};

use crate::grpc::SOL_PRICE;

use crate::event::SolanaEvent;


pub fn get_mc(base_mint: &str,base_amount : i64, quote_amount: i64) -> f64 {

    
    if base_mint == "So11111111111111111111111111111111111111112" {
        base_amount as f64 / quote_amount as f64 * SOL_PRICE * 1_000_000.0
    } else {
        quote_amount as f64 / base_amount as f64 * SOL_PRICE * 1_000_000.0
    }

}

pub static EVENT_BUS: Lazy<Mutex<Option<mpsc::Sender<SolanaEvent>>>> =
    Lazy::new(|| Mutex::new(None));

pub async fn init_event_bus() {
    let (tx, mut rx) = mpsc::channel(100);

    task::spawn(async move {
        while let Some(event) = rx.recv().await {
            handle_event(event).await;
        }
    });

    let mut bus = EVENT_BUS.lock().await;
    *bus = Some(tx);
}

pub async fn emit_event(event: SolanaEvent) {
    if let Some(sender) = EVENT_BUS.lock().await.as_ref() {
        let _ = sender.send(event).await;
    } else {
        eprintln!("❌ Event bus not initialized");
    }
}

async fn handle_event(event: SolanaEvent) {
    

    let current_time = chrono::Local::now()
        .naive_local()
        .format("%Y-%m-%d %H:%M:%S")
        .to_string();

    //check if dev in table
    // if not create dev_entity and wallet
    //then create dev_token_creation

    // Check if dev wallet exists

    match event.event_type {
        EventType::CreatePool => {
            println!("🟢 Handling CreateEvent: {:?}", event);
            let wallet_exists = get_dev_wallet(&event.creator.as_ref().unwrap()).unwrap();
            let mut entity_id: i32 = 0;

            if wallet_exists.is_none() {
                // Create new dev entity
                let dev_entity = DevEntity {
                    id: None,
                    label: event.creator.clone(),
                    score: Some(0),
                    note: Some(String::new()),
                    max_hold: Some(999),
                    max_pct: Some(10.0),
                    max_sol: Some(0.007),
                    created_at: Some(chrono::Utc::now().timestamp() as i64),
                    enabled: Some(0),
                    num_trades: Some(0),
                    num_wins: Some(0),
                    num_losses: Some(0),
                    profit_pct: Some(0.0),
                    profit_usd: Some(0.0),
                };

                entity_id = create_dev_entity(&dev_entity).unwrap();
                println!("New Entity ID: {}", entity_id);

                // Create new dev wallet
                let dev_wallet = DevWallet {
                    id: None,
                    address: event.creator.clone().unwrap(),
                    parent_wallet: "".to_string(),
                    dev_entity_id: entity_id.clone(),
                    scanned_at: Some(1),
                    withdraw_at: Some(1),
                    created_at: Some(chrono::Utc::now().timestamp() as i64),
                    scan: Some(0),
                };

                create_dev_wallet(&dev_wallet).unwrap();
            } else {
                // get walle entinty id from devWallet
                entity_id = wallet_exists.clone().unwrap().dev_entity_id;
                println!("Existing Entity ID: {}", entity_id);
                if let Some(mut wallet) = wallet_exists {
                    wallet.scan = Some(0);
                    update_dev_wallet(&wallet).unwrap();
                }

            }


            println!("Entity ID: {}", entity_id);

            // Extract mint value once to avoid borrow issues
            let mint_address = event.mint.unwrap_or_default();

            let dev_creation: DevTokenCreation = DevTokenCreation {
                id: None,
                dev_entity_id: entity_id,
                mint: mint_address.clone(),
                creator: event.creator.unwrap_or_default(),
                base_mint: event.base_mint.unwrap_or_default(),
                quote_mint: event.quote_mint.unwrap_or_default(),
                base_amount: event.base_amount.unwrap_or_default() ,
                quote_amount: event.quote_amount.unwrap_or_default() ,
                sol_liquidity: event.sol_liquidity.unwrap_or_default(),
                tx_amount: Some(0),
                volume: Some(0.0),
                ath_mcap: Some(0.0),
                ath_at: Some(0),
                ath_pct: Some(0.0),
                withdraw_at: Some(0),
                withdraw_pct: Some(0.0),
                withdraw_mcap: Some(0.0),
                created_at: None,
                is_running: Some(1),
                start_price: event.start_price.unwrap_or_default(),
                start_mcap: event.market_cap.unwrap_or_default(),
                multi_tx: Some(0),
                single_tx: Some(0),
                unit_limit: Some("".to_string()),
                unit_price: Some("".to_string()),
                tip_amount: Some(0.0),
                bot_amount: Some(0),
                bot_sol_amount: Some(0.0),

            };

            // get get_dev_entity max_pct 
            let dev_entity = get_dev_entity(entity_id).unwrap().unwrap();
            let max_pct = dev_entity.max_pct.unwrap_or(10.0);
            let enabled = dev_entity.enabled.unwrap_or(0);

            if enabled == 1 {
                //update mint_data.enabled = 1 and max_pct
                
                println!("Enabled: {} mint:{} ", enabled , mint_address );
                //let mut mint_data = get_mint_data(&mint_address).unwrap();
                
                //mint_data.enabled = true;
                //mint_data.max_pct = max_pct as f64;
                //update_mint_data(&mint_address, mint_data.clone());
            }


            if let Err(e) = create_dev_token_creation(&dev_creation) {
                eprintln!("❌ Failed to create dev token creation: {:?}", e);
            }
        }
        EventType::Buy => {
            println!("Buy");
        }
        EventType::Sell => {
            println!("Sell");
        }
        EventType::Withdraw => {
            println!("❌ Handling WITHDRAW Evebnt: {:?}", event);
            let wallet_exists = get_dev_wallet(&event.creator.as_ref().unwrap()).unwrap();
            
            // update wallet set scan=1 use with function update_dev_wallet
            if let Some(mut wallet) = wallet_exists {
                wallet.scan = Some(1);
                wallet.withdraw_at = Some(chrono::Utc::now().timestamp() as i64);
                update_dev_wallet(&wallet).unwrap();
            }
          
             let mint_data=get_mint_data(&event.mint.as_ref().unwrap()).unwrap();

          let dev_creation = get_dev_token_creation(&event.mint.as_ref().unwrap()).unwrap();
            if let Some(mut creation) = dev_creation {


                //println!("WITHDRAWN creation: {:?}", creation);
                println!("WITHDRAWN mintdata: {:?}", mint_data);

                let mc = get_mc(&mint_data.base_mint, creation.base_amount, creation.quote_amount);
                //println!("WITHDRAWN mc: {:?}", mc);
                creation.tx_amount = Some(mint_data.num_of_trades as i32);
                creation.volume = Some(mint_data.volume as f32);
                creation.withdraw_at = Some(chrono::Utc::now().timestamp() as i64);
                creation.is_running = Some(0);
                creation.withdraw_mcap = Some(mint_data.market_cap as f32);
                creation.ath_mcap = Some(mint_data.ath_mcap);
                creation.multi_tx = Some(mint_data.multi_tx);
                creation.single_tx = Some(mint_data.single_tx);
                let (unit_limit, unit_price) = mint_data.unit_occurrences.iter()
                    .max_by_key(|&(_, count)| count)
                    .map(|(unit, _)| {
                        let unit_str = unit.to_string();
                        let parts: Vec<&str> = unit_str.split('_').collect();
                        if parts.len() >= 2 {
                            (parts[0].to_string(), parts[1].to_string())
                        } else {
                            (unit_str.clone(), "".to_string())
                        }
                    })
                    .unwrap_or(("".to_string(), "".to_string()));
                creation.unit_limit = Some(unit_limit);
                creation.unit_price = Some(unit_price);


                update_dev_token_creation(&creation).unwrap();
            }

            

        }
    }
}
