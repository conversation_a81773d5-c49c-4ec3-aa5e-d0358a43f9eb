use anyhow::{Context, Result};
use chrono::NaiveDateTime;
use dotenv::dotenv;
use lazy_static::lazy_static;
use mysql::prelude::*;
use mysql::*;
use std::env;
use std::sync::Arc;

// Global MySQL connection pool
lazy_static! {
    static ref MYSQL_POOL: Arc<Result<Pool, anyhow::Error>> = Arc::new(create_mysql_pool());
}

// User struct to hold data from database
#[derive(Debug, Clone)]
pub struct User {
    pub id: i32,
    pub username: String,
    pub password: String, // Note: In a real application, you'd never store plaintext passwords
    pub is_active: bool,
}

// Struct to represent a transaction from the token_tx table
#[derive(Debug, Clone)]
pub struct Transaction {
    pub id: i32,
    pub mint: String,
    pub account: String,
    pub signer: String,
    pub sol_amount: Option<String>,
    pub token_amount: Option<String>,
    pub trade: Option<String>,
    pub price: Option<f64>,
    pub market_cup: Option<i32>,
    pub block_time: i32,
    pub block_id: i32,
    pub signature: String,
    pub app: Option<String>,
}

// Struct to represent a configuration from the app_configs table
use serde::Serialize;

#[derive(Serialize, Debug, Clone)]
pub struct AppConfig {
    pub enabled: i32,       // Changed from bool to i32
    pub is_simulation: i32, // Changed from bool to i32
    pub buy_sol: f64,
    pub max_time_sec: i32,
    pub min_time_sec: i32,
    pub max_profit_sol: f64,
    pub min_profit_sol: f64,
    pub max_user_buy_sol: f64,
    pub max_mc_pct: i32,
    pub min_mc_pct: i32,
    pub address: String,
    pub platform: String,
}

// Function to load environment variables from .env file if not already loaded
fn ensure_env_loaded() {
    match dotenv() {
        Ok(_) => (),
        Err(e) => eprintln!("Warning: Could not load .env file: {}", e),
    }
}

// Function to create the MySQL connection pool
fn create_mysql_pool() -> Result<Pool, anyhow::Error> {
    // Ensure environment variables are loaded from .env
    ensure_env_loaded();

    // Read database connection parameters with defaults
    let db_host = env::var("MYSQL_HOST").unwrap_or_else(|_| "localhost".to_string());
    let db_port = env::var("MYSQL_PORT").unwrap_or_else(|_| "3306".to_string());
    let db_user = env::var("MYSQL_USER").unwrap_or_else(|_| "root".to_string());
    let db_password = env::var("MYSQL_PASSWORD").unwrap_or_default();
    let db_name =
        env::var("MYSQL_DATABASE").context("MYSQL_DATABASE environment variable not set")?;

    println!(
        "Setting up MySQL connection pool for {}:{}/{}",
        db_host, db_port, db_name
    );

    // Build connection URL
    let url = format!(
        "mysql://{}:{}@{}:{}/{}",
        db_user, db_password, db_host, db_port, db_name
    );

    // Create connection pool with appropriate options
    let opts = Opts::from_url(&url)?;
    let pool_opts = PoolOpts::default().with_constraints(PoolConstraints::new(1, 10).unwrap()); // Min 1, max 10 connections

    let pool = Pool::new(opts)?;

    Ok(pool)
}

// Function to initialize the database connection pool and ensure it works
pub fn init_database() -> Result<()> {
    let pool = MYSQL_POOL
        .as_ref()
        .as_ref()
        .map_err(|e| anyhow::anyhow!("{}", e))?;

    // Get connection from pool to verify it works
    let mut conn = pool.get_conn()?;
    let result: Option<String> =
        conn.query_first("SELECT 'Database pool initialized successfully' as result")?;

    if let Some(message) = result {
        //println!("{}", message);
        Ok(())
    } else {
        Err(anyhow::anyhow!("Failed to initialize database pool"))
    }
}

// Function to get a connection from the global pool
fn get_db_conn() -> Result<PooledConn> {
    let pool = MYSQL_POOL
        .as_ref()
        .as_ref()
        .map_err(|e| anyhow::anyhow!("{}", e))?;
    let conn = pool
        .get_conn()
        .context("Failed to get MySQL connection from pool")?;
    Ok(conn)
}

// Function to get all users from database
pub fn get_all_users() -> Result<Vec<User>> {
    let mut conn = get_db_conn()?;

    let users = conn.query_map(
        "SELECT id, username, password, is_active FROM users",
        |(id, username, password, is_active)| User {
            id,
            username,
            password,
            is_active,
        },
    )?;

    Ok(users)
}

// Function to get a user by username
pub fn get_user_by_username(username: &str) -> Result<Option<User>> {
    let mut conn = get_db_conn()?;

    let mut users = conn.exec_map(
        "SELECT id, username, password, is_active FROM users WHERE username = :username",
        params! {
            "username" => username,
        },
        |(id, username, password, is_active)| User {
            id,
            username,
            password,
            is_active,
        },
    )?;

    // Return the first user found (or None if no users found)
    Ok(users.pop())
}

// Function to update a user's username
pub fn update_username(user_id: i32, new_username: &str) -> Result<bool> {
    let mut conn = get_db_conn()?;

    conn.exec_drop(
        "UPDATE users SET username = :new_username WHERE id = :user_id",
        params! {
            "new_username" => new_username,
            "user_id" => user_id,
        },
    )?;

    Ok(conn.affected_rows() > 0)
}

// Function to update a user's password
pub fn update_password(user_id: i32, new_password: &str) -> Result<bool> {
    let mut conn = get_db_conn()?;

    // In a real application, you would hash the password before storing it
    conn.exec_drop(
        "UPDATE users SET password = :new_password WHERE id = :user_id",
        params! {
            "new_password" => new_password,
            "user_id" => user_id,
        },
    )?;

    Ok(conn.affected_rows() > 0)
}

// Function to verify user credentials
pub fn verify_user_credentials(username: &str, password: &str) -> Result<bool> {
    if let Some(user) = get_user_by_username(username)? {
        // In a real application, you would compare hashed passwords
        Ok(user.password == password && user.is_active)
    } else {
        Ok(false)
    }
}

// Example function to get enabled accounts from database for GRPC
pub async fn get_enabled_accounts_for_grpc() -> Result<Vec<String>> {
    let mut conn = get_db_conn()?;

    let accounts: Vec<String> = conn
        .query_map(
            "SELECT account_address FROM grpc_monitored_accounts WHERE is_enabled = 1",
            |account_address| account_address,
        )
        .context("Failed to query enabled GRPC accounts")?;

    println!(
        "Found {} enabled accounts for GRPC monitoring",
        accounts.len()
    );
    Ok(accounts)
}

// Function to get a transaction by ID
pub fn get_transaction(transaction_id: i32) -> Result<Option<Transaction>> {
    let mut conn = get_db_conn()?;

    let mut transactions = conn.exec_map(
        "SELECT id, mint, account, signer, sol_amount, token_amount, trade, price, market_cup, block_time, block_id, signature, app 
         FROM token_tx 
         WHERE id = :transaction_id",
        params! {
            "transaction_id" => transaction_id,
        },
        |row: Row| {
            Transaction {
                id: row.get("id").unwrap(),
                mint: row.get("mint").unwrap(),
                account: row.get("account").unwrap(),
                signer: row.get("signer").unwrap(),
                sol_amount: row.get("sol_amount"),
                token_amount: row.get("token_amount"),
                trade: row.get("trade"),
                price: row.get("price"),
                market_cup: row.get("market_cup"),
                block_time: row.get("block_time").unwrap(),
                block_id: row.get("block_id").unwrap(),
                signature: row.get("signature").unwrap(),
                app: row.get("app"),
            }
        },
    )?;

    // Return the first transaction found (or None if no transactions found)
    Ok(transactions.pop())
}

// Function to get all transactions from the token_tx table
pub fn get_all_transactions() -> Result<Vec<Transaction>> {
    let mut conn = get_db_conn()?;

    let transactions = conn.query_map(
        "SELECT id, mint, account, signer, sol_amount, token_amount, trade, price, market_cup, block_time, block_id, signature, app 
         FROM token_tx",
        |row: Row| {
            Transaction {
                id: row.get("id").unwrap(),
                mint: row.get("mint").unwrap(),
                account: row.get("account").unwrap(),
                signer: row.get("signer").unwrap(),
                sol_amount: row.get("sol_amount"),
                token_amount: row.get("token_amount"),
                trade: row.get("trade"),
                price: row.get("price"),
                market_cup: row.get("market_cup"),
                block_time: row.get("block_time").unwrap(),
                block_id: row.get("block_id").unwrap(),
                signature: row.get("signature").unwrap(),
                app: row.get("app"),
            }
        },
    )?;

    Ok(transactions)
}

// Function to get all configurations from the app_configs table
pub fn get_config() -> Result<Vec<(String, AppConfig)>> {
    let mut conn = get_db_conn()?;

    let configs = conn.query_map(
        "SELECT app, enabled, is_simulation, buy_sol, max_time_sec, min_time_sec, max_profit_sol, min_profit_sol, max_user_buy_sol, max_mc_pct, min_mc_pct, address, platform FROM app_config",
        |row: Row| {
            let app: String = row.get("app").unwrap();
            let enabled: i32 = row.get("enabled").unwrap(); // Changed type to i32
            let is_simulation: i32 = row.get("is_simulation").unwrap(); // Changed type to i32
            let buy_sol: f64 = row.get("buy_sol").unwrap();
            let max_time_sec: i32 = row.get("max_time_sec").unwrap();
            let min_time_sec: i32 = row.get("min_time_sec").unwrap();
            let max_profit_sol: f64 = row.get("max_profit_sol").unwrap();
            let min_profit_sol: f64 = row.get("min_profit_sol").unwrap();
            let max_user_buy_sol: f64 = row.get("max_user_buy_sol").unwrap();
            let max_mc_pct: i32 = row.get("max_mc_pct").unwrap();
            let min_mc_pct: i32 = row.get("min_mc_pct").unwrap();
            let address: String = row.get("address").unwrap();
            let platform: String = row.get("platform").unwrap();

            (
                app,
                AppConfig {
                    enabled,
                    is_simulation,
                    buy_sol,
                    max_time_sec,
                    min_time_sec,
                    max_profit_sol,
                    min_profit_sol,
                    max_user_buy_sol,
                    max_mc_pct,
                    min_mc_pct,
                    address,
                    platform,
                },
            )
        },
    )?;

    Ok(configs)
}

// Function to initialize database connection for testing
pub fn test_database_connection() -> Result<()> {
    let mut conn = get_db_conn()?;

    // Simple test query
    let result: Option<String> = conn.query_first("SELECT 'Connected successfully' as result")?;

    if let Some(message) = result {
        println!("Database connection test: {}", message);
        Ok(())
    } else {
        Err(anyhow::anyhow!(
            "Database connection test failed: No result returned"
        ))
    }
}

// Example of MySQL connection string format for documentation
// mysql://username:password@hostname:port/database_name

// Example usage in main.rs:
//
// use crate::db;
//
// async fn main() -> Result<()> {
//     // Initialize database pool
//     db::init_database()?;
//
//     // Get and print all users
//     let users = db::get_all_users()?;
//     for user in &users {
//         println!("User: {} (ID: {})", user.username, user.id);
//     }
//
//     // Verify user credentials
//     if db::verify_user_credentials("admin", "password123")? {
//         println!("Login successful");
//     } else {
//         println!("Invalid credentials");
//     }
//
//     // Update a user's username
//     if db::update_username(1, "new_admin_name")? {
//         println!("Username updated successfully");
//     } else {
//         println!("Failed to update username");
//     }
//
//     // Get enabled GRPC accounts
//     let accounts = db::get_enabled_accounts_for_grpc().await?;
//     println!("Monitoring {} accounts", accounts.len());
//
//     // Test database connection
//     db::test_database_connection()?;
//
//     Ok(())
// }

// Struct to represent a dev token creation from dev_token_creations table
#[derive(Debug, Clone)]
pub struct DevTokenCreation {
    pub id: Option<i32>,
    pub dev_entity_id: i32,
    pub creator: String,
    pub mint: String,
    pub base_mint: String,
    pub quote_mint: String,
    pub base_amount: i64,
    pub quote_amount: i64,
    pub sol_liquidity: f64,
    pub tx_amount: Option<i32>,
    pub volume: Option<f32>,
    pub ath_mcap: Option<f64>,
    pub ath_at: Option<i64>,
    pub ath_pct: Option<f32>,
    pub withdraw_at: Option<i64>,
    pub withdraw_pct: Option<f32>,
    pub withdraw_mcap: Option<f32>,
    pub created_at: Option<i64>,
    pub is_running: Option<i32>,
    pub start_price: f64,
    pub start_mcap: f64,
    pub multi_tx: Option<i32>,
    pub single_tx: Option<i32>,
    pub unit_limit: Option<String>,
    pub unit_price: Option<String>,
    pub tip_amount: Option<f64>,
    pub bot_amount: Option<i32>,
    pub bot_sol_amount: Option<f64>,
}

// Function to create a new dev token creation record
pub fn create_dev_token_creation(creation: &DevTokenCreation) -> Result<i32> {
    let mut conn = get_db_conn()?;

    let now = chrono::Local::now().timestamp() as i64;

    conn.exec_drop(
        r"INSERT INTO dev_token_creations (
            dev_entity_id, mint, creator, base_mint, quote_mint,
            base_amount, quote_amount, sol_liquidity,
            tx_amount, volume, ath_mcap, ath_at, ath_pct,
            withdraw_at, withdraw_pct, withdraw_mcap,
            created_at, is_running, start_price, start_mcap, multi_tx, single_tx, unit_limit, unit_price, tip_amount, bot_amount, bot_sol_amount
        ) VALUES (
            :dev_entity_id, :mint, :creator, :base_mint, :quote_mint,
            :base_amount, :quote_amount, :sol_liquidity,
            :tx_amount, :volume, :ath_mcap, :ath_at, :ath_pct,
            :withdraw_at, :withdraw_pct, :withdraw_mcap,
            :created_at, :is_running, :start_price, :start_mcap, :multi_tx, :single_tx, :unit_limit, :unit_price, :tip_amount, :bot_amount, :bot_sol_amount
        )",
        params! {
            "dev_entity_id" => creation.dev_entity_id,
            "mint" => &creation.mint,
            "creator" => &creation.creator,
            "base_mint" => &creation.base_mint,
            "quote_mint" => &creation.quote_mint,
            "base_amount" => creation.base_amount,
            "quote_amount" => creation.quote_amount,
            "sol_liquidity" => creation.sol_liquidity,
            "tx_amount" => creation.tx_amount,
            "volume" => creation.volume,
            "ath_mcap" => creation.ath_mcap,
            "ath_at" => creation.ath_at.clone(),
            "ath_pct" => creation.ath_pct,
            "withdraw_at" => creation.withdraw_at.clone(),
            "withdraw_pct" => creation.withdraw_pct,
            "withdraw_mcap" => creation.withdraw_mcap,
            "created_at" => now,
            "is_running" => creation.is_running,
            "start_price" => creation.start_price,
            "start_mcap" => creation.start_mcap,
            "multi_tx" => creation.multi_tx,
            "single_tx" => creation.single_tx,
            "unit_limit" => creation.unit_limit.clone(),
            "unit_price" => creation.unit_price.clone(),
            "tip_amount" => creation.tip_amount,
            "bot_amount" => creation.bot_amount,
            "bot_sol_amount" => creation.bot_sol_amount,
        },
    )?;

    Ok(conn.last_insert_id() as i32)
}

// Function to update an existing dev token creation record
pub fn update_dev_token_creation(creation: &DevTokenCreation) -> Result<bool> {
    let mut conn = get_db_conn()?;

    if creation.id.is_none() {
        return Err(anyhow::anyhow!("Cannot update record without ID"));
    }

    conn.exec_drop(
        r"UPDATE dev_token_creations SET
            dev_entity_id = :dev_entity_id,
            creator = :creator,
            base_mint = :base_mint,
            quote_mint = :quote_mint,
            base_amount = :base_amount,
            quote_amount = :quote_amount,
            sol_liquidity = :sol_liquidity,
            tx_amount = :tx_amount,
            volume = :volume,
            ath_mcap = :ath_mcap,
            ath_at = :ath_at,
            ath_pct = :ath_pct,
            withdraw_at = :withdraw_at,
            withdraw_pct = :withdraw_pct,
            withdraw_mcap = :withdraw_mcap,
            created_at = :created_at,
            is_running = :is_running,
            start_price = :start_price,
            start_mcap = :start_mcap,
            multi_tx = :multi_tx,
            single_tx = :single_tx,
            unit_limit = :unit_limit,
            unit_price = :unit_price,
            tip_amount = :tip_amount,
            bot_amount = :bot_amount,
            bot_sol_amount = :bot_sol_amount
        WHERE id = :id",
        params! {
            "id" => creation.id,
            "dev_entity_id" => creation.dev_entity_id,
            "creator" => &creation.creator,
            "base_mint" => &creation.base_mint,
            "quote_mint" => &creation.quote_mint,
            "base_amount" => creation.base_amount,
            "quote_amount" => creation.quote_amount,
            "sol_liquidity" => creation.sol_liquidity,
            "tx_amount" => creation.tx_amount,
            "volume" => creation.volume,
            "ath_mcap" => creation.ath_mcap,
            "ath_at" => creation.ath_at.clone(),
            "ath_pct" => creation.ath_pct,
            "withdraw_at" => creation.withdraw_at.clone(),
            "withdraw_pct" => creation.withdraw_pct,
            "withdraw_mcap" => creation.withdraw_mcap.clone(),
            "created_at" => creation.created_at.clone(),
            "is_running" => creation.is_running,
            "start_price" => creation.start_price,
            "start_mcap" => creation.start_mcap,
            "multi_tx" => creation.multi_tx,
            "single_tx" => creation.single_tx,
            "unit_limit" => creation.unit_limit.clone(),
            "unit_price" => creation.unit_price.clone(),
            "tip_amount" => creation.tip_amount,
            "bot_amount" => creation.bot_amount,
            "bot_sol_amount" => creation.bot_sol_amount,
        },
    )?;

    Ok(conn.affected_rows() > 0)
}

// Function to get a dev token creation by ID
pub fn get_dev_token_creation(mint: &str) -> Result<Option<DevTokenCreation>> {
    let mut conn = get_db_conn()?;

    let mut creations = conn.exec_map(
        "SELECT id, dev_entity_id, mint, creator, base_mint, quote_mint,
                base_amount,
                quote_amount,
                sol_liquidity,
                tx_amount, 
                volume, 
                ath_mcap, 
                ath_pct, 
                withdraw_pct, 
                withdraw_mcap, 
                is_running,
                start_price,
                start_mcap,
                ath_at,
                withdraw_at,
                created_at,
                multi_tx,
                single_tx,
                unit_limit,
                unit_price,
                tip_amount,
                bot_amount,
                bot_sol_amount
         FROM dev_token_creations WHERE mint = :mint",
        params! {
            "mint" => mint,
        },
        |row: Row| DevTokenCreation {
            id: Some(row.get("id").unwrap()),
            dev_entity_id: row.get("dev_entity_id").unwrap(),
            mint: row.get("mint").unwrap(),
            creator: row.get("creator").unwrap(),
            base_mint: row.get("base_mint").unwrap(),
            quote_mint: row.get("quote_mint").unwrap(),
            base_amount: row.get("base_amount").unwrap(),
            quote_amount: row.get("quote_amount").unwrap(),
            sol_liquidity: row.get("sol_liquidity").unwrap(),
            tx_amount: row.get("tx_amount"),
            volume: row.get("volume"),
            ath_mcap: row.get("ath_mcap"),
            ath_at: row.get("ath_at"),
            ath_pct: row.get("ath_pct"),
            withdraw_at: row.get("withdraw_at"),
            withdraw_pct: row.get("withdraw_pct"),
            withdraw_mcap: row.get("withdraw_mcap"),
            created_at: row.get("created_at"),
            is_running: row.get("is_running"),
            start_price: row.get("start_price").unwrap(),
            start_mcap: row.get("start_mcap").unwrap(),
            multi_tx: row.get("multi_tx"),
            single_tx: row.get("single_tx"),
            unit_limit: row.get("unit_limit"),
            unit_price: row.get("unit_price"),
            tip_amount: row.get("tip_amount"),
            bot_amount: row.get("bot_amount"),
            bot_sol_amount: row.get("bot_sol_amount"),
        },
    )?;

    Ok(creations.pop())
}

// Struct to represent a dev wallet from dev_wallets table
#[derive(Debug, Clone)]
pub struct DevWallet {
    pub id: Option<i32>,
    pub address: String,
    pub parent_wallet: String,
    pub dev_entity_id: i32,
    pub created_at: Option<i64>,
    pub scanned_at: Option<i64>,
    pub withdraw_at: Option<i64>,
    pub scan: Option<i32>,

}

// Function to create a new dev wallet record
pub fn create_dev_wallet(wallet: &DevWallet) -> Result<i32> {
    let mut conn = get_db_conn()?;
    let now = chrono::Local::now().timestamp() as i64;
    // Convert current timestamp to MySQL datetime format
    //let current_time = chrono::Local::now().naive_local().format("%Y-%m-%d %H:%M:%S").to_string();

    conn.exec_drop(
        r"INSERT INTO dev_wallets (
            address, parent_wallet, dev_entity_id, scan, created_at, scanned_at, withdraw_at
        ) VALUES (
            :address, :parent_wallet, :dev_entity_id, :scan, :created_at, :scanned_at, :withdraw_at
        )",
        params! {
            "address" => &wallet.address,
            "parent_wallet" => &wallet.parent_wallet,
            "dev_entity_id" => wallet.dev_entity_id,
            "scan" => wallet.scan,
            "created_at" => now,
            "scanned_at" => 1,
            "withdraw_at" => 1,

        },
    )?;

    Ok(conn.last_insert_id() as i32)
}

// Function to update an existing dev wallet record
pub fn update_dev_wallet(wallet: &DevWallet) -> Result<bool> {
    let mut conn = get_db_conn()?;

    if wallet.id.is_none() {
        return Err(anyhow::anyhow!("Cannot update record without ID"));
    }

    conn.exec_drop(
        r"UPDATE dev_wallets SET
            address = :address,
            parent_wallet = :parent_wallet,
            dev_entity_id = :dev_entity_id,
            scanned_at = :scanned_at,
            withdraw_at = :withdraw_at,
            scan = :scan
        WHERE id = :id",
        params! {
            "id" => wallet.id,
            "address" => &wallet.address,
            "parent_wallet" => &wallet.parent_wallet,
            "dev_entity_id" => wallet.dev_entity_id,
            "scanned_at" => wallet.scanned_at,
            "withdraw_at" => wallet.withdraw_at,
            "scan" => wallet.scan,
        },
    )?;

    Ok(conn.affected_rows() > 0)
}

// Function to get a dev wallet by ID
pub fn get_dev_wallet(address: &str) -> Result<Option<DevWallet>> {
    let mut conn = get_db_conn()?;
    let now = chrono::Local::now().timestamp() as i64;

    let mut wallets = conn.exec_map(
        "SELECT id, address, parent_wallet, dev_entity_id, scan,
                 created_at
         FROM dev_wallets WHERE address = :address",
        params! {
            "address" => address,
        },
        |row: Row| DevWallet {
            id: Some(row.get("id").unwrap()),
            address: row.get("address").unwrap(),
            parent_wallet: row.get("parent_wallet").unwrap(),
            dev_entity_id: row.get("dev_entity_id").unwrap(),
            scanned_at: row.get("scanned_at"),
            withdraw_at: row.get("withdraw_at"),
            scan: row.get("scan"),
            created_at: row.get("created_at"),
        },
    )?;

    Ok(wallets.pop())
}

// Function to update a single field in dev wallet record
pub fn update_dev_wallet_field(id: i32, field: &str, value: &str) -> Result<bool> {
    let mut conn = get_db_conn()?;

    // List of allowed fields to prevent SQL injection
    let allowed_fields = vec!["address", "parent_wallet", "dev_entity_id", "scan"];

    if !allowed_fields.contains(&field) {
        return Err(anyhow::anyhow!("Invalid field name"));
    }

    let query = format!("UPDATE dev_wallets SET {} = :value WHERE id = :id", field);

    conn.exec_drop(
        &query,
        params! {
            "value" => value,
            "id" => id,
        },
    )?;

    Ok(conn.affected_rows() > 0)
}

// Struct to represent a dev entity from dev_entities table
#[derive(Debug, Clone)]
pub struct DevEntity {
    pub id: Option<i32>,
    pub created_at: Option<i64>,
    pub label: Option<String>,
    pub score: Option<i32>,
    pub note: Option<String>,
    pub max_hold: Option<i32>,
    pub max_pct: Option<f32>,
    pub max_sol: Option<f32>,
    pub enabled: Option<i32>,
    pub num_trades: Option<i32>,
    pub num_wins: Option<i32>,
    pub num_losses: Option<i32>,
    pub profit_pct: Option<f32>,
    pub profit_usd: Option<f32>,
}

// Function to create a new dev entity record
pub fn create_dev_entity(entity: &DevEntity) -> Result<i32> {
    let mut conn = get_db_conn()?;
    let now = chrono::Local::now().timestamp() as i64;
    // Convert current timestamp to MySQL datetime format
    // let current_time = chrono::Local::now().naive_local().format("%Y-%m-%d %H:%M:%S").to_string();


//missing fields: num_trades	num_wins	num_losses	profit_pct	profit_usd 

    conn.exec_drop(
        r"INSERT INTO dev_entities (
            label, score, note, max_hold, max_pct, max_sol, created_at, enabled, num_trades, num_wins, num_losses, profit_pct, profit_usd
        ) VALUES (
            :label, :score, :note, :max_hold, :max_pct, :max_sol, :created_at, :enabled, :num_trades, :num_wins, :num_losses, :profit_pct, :profit_usd
        )",
        params! {
            "num_trades" => 0,
            "num_wins" => 0,
            "num_losses" => 0,
            "profit_pct" => 0.0,
            "profit_usd" => 0.0,
            "label" => &entity.label,
            "score" => entity.score,
            "note" => &entity.note,
            "max_hold" => entity.max_hold,
            "max_pct" => entity.max_pct,
            "max_sol" => entity.max_sol,
            "created_at" => now,
            "enabled" => 0,
        },
    )?;

    Ok(conn.last_insert_id() as i32)
}

// Function to update an existing dev entity record
pub fn update_dev_entity(entity: &DevEntity) -> Result<bool> {
    let mut conn = get_db_conn()?;

    if entity.id.is_none() {
        return Err(anyhow::anyhow!("Cannot update record without ID"));
    }

    conn.exec_drop(
        r"UPDATE dev_entities SET
            label = :label,
            score = :score,
            note = :note,
            max_hold = :max_hold,
            max_pct = :max_pct,
            max_sol = :max_sol
        WHERE id = :id",
        params! {
            "id" => entity.id,
            "label" => &entity.label,
            "score" => entity.score,
            "note" => &entity.note,
            "max_hold" => entity.max_hold,
            "max_pct" => entity.max_pct,
            "max_sol" => entity.max_sol,
        },
    )?;

    Ok(conn.affected_rows() > 0)
}

// Function to get a dev entity by ID
pub fn get_dev_entity(id: i32) -> Result<Option<DevEntity>> {
    let mut conn = get_db_conn()?;

    let mut entities = conn.exec_map(
        "SELECT id, label, score, note, max_hold, max_pct, max_sol, enabled, num_trades, num_wins, num_losses, profit_pct, profit_usd, UNIX_TIMESTAMP(created_at) as created_at
         FROM dev_entities WHERE id = :id",
        params! {
            "id" => id,
        },
        |row: Row| DevEntity {
            id: Some(row.get("id").unwrap()),
            label: row.get("label"),
            score: row.get("score"),
            note: row.get("note"),
            max_hold: row.get("max_hold"),
            max_pct: row.get("max_pct"),
            max_sol: row.get("max_sol"),
            enabled: row.get("enabled"),
            num_trades: row.get("num_trades"),
            num_wins: row.get("num_wins"),
            num_losses: row.get("num_losses"),
            profit_pct: row.get("profit_pct"),
            profit_usd: row.get("profit_usd"),
            created_at: row.get("created_at"),
        },
    )?;

    Ok(entities.pop())
}

// Function to update a single field in dev entity record
pub fn update_dev_entity_field(id: i32, field: &str, value: &str) -> Result<bool> {
    let mut conn = get_db_conn()?;

    // List of allowed fields to prevent SQL injection
    let allowed_fields = vec!["label", "score", "note", "max_hold", "max_pct", "max_sol"];

    if !allowed_fields.contains(&field) {
        return Err(anyhow::anyhow!("Invalid field name"));
    }

    let query = format!("UPDATE dev_entities SET {} = :value WHERE id = :id", field);

    conn.exec_drop(
        &query,
        params! {
            "value" => value,
            "id" => id,
        },
    )?;

    Ok(conn.affected_rows() > 0)
}
