
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum EventType {
    <PERSON><PERSON><PERSON><PERSON>,
    Buy,
    Sell,
    Withdraw,
}

#[derive(Debug, <PERSON>lone)]
pub struct SolanaEvent {
    pub event_type: EventType,
    pub mint: Option<String>,
    pub pool: Option<String>, // Mint address as a string
    pub sol_liquidity: Option<f64>, // Amount of SOL liquidity in the pool
    pub base_mint: Option<String>,
    pub quote_mint: Option<String>,
    pub base_amount: Option<i64>,
    pub quote_amount: Option<i64>,
    pub sol_reserves: Option<f64>, // SOL reserves in the pool
    pub token_reserves: Option<f64>, // Token reserves in the pool
    pub creator: Option<String>, // Optional creator address
    pub market_cap: Option<f64>,
    pub start_price: Option<f64>,
    pub start_price_base: Option<f64>,
    pub dev_entity_id: Option<i32>,
    pub multi_tx: Option<i32>, // Number of multiple transactions
    pub single_tx: Option<i32>, // Number of single transactions
    pub unit_limit: Option<String>, // Unit limit as a string
    pub unit_price: Option<String>, // Unit price as a string
    pub tip_amount: Option<f64>, // Tip amount in float
    pub bot_amount: Option<i32>, // Bot amount as an integer
    pub bot_sol_amount: Option<f64>, 
}


